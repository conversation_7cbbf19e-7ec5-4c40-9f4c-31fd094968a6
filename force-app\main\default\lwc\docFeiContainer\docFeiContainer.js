import { LightningElement, wire, track, api} from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';
import { CurrentPageReference } from 'lightning/navigation';
import { CloseActionScreenEvent } from 'lightning/actions';
import pubsub from "omnistudio/pubsub";

export default class DocFeiContainer extends LightningElement {

    @api FiscalCode;
    @api permissionSetName;
    @api FEIID;
    @api feiRequestPayload;
    isLWCModalOpened = false;

    params = {
        feiId: null,
        Fiscalcode: null,
        feiRequestPayload: null,
        permissionSetName: null
    }

    async connectedCallback(){ 
        await new Promise(resolve =>
            setTimeout(resolve, 500)
        );
        console.log('### connectedCallback');
        console.log('### PreEncoded feiRequestPayload: ' + this.feiRequestPayload);
        this.params.feiId = this.FEIID;
        console.log('### FEIID: ' + this.FEIID);
        this.params.Fiscalcode = this.FiscalCode;
        console.log('### FiscalCode: ' + this.FiscalCode);
        this.params.feiRequestPayload = this.parseQueryString(this.feiRequestPayload);
        console.log('### feiRequestPayload: ' + this.params.feiRequestPayload);
        this.params.permissionSetName = this.permissionSetName;
        console.log('### permissionSetName: ' + this.permissionSetName);

        window.addEventListener("message", evt => {

            console.log('event ok docFeiContainer ok');


            if(evt.data.type == "feiFlowFinished"){
                console.log('### DocfeiFlowFinished');
                pubsub.fire("feiContainerEvent", "closeFeiContainer", { /* payload */ });
                this.dispatchEvent(new CloseActionScreenEvent({ bubbles: true, composed: true }));
                console.log('### DocfeiFlowFinished - CloseActionScreenEvent dispatched');
            }

        });

        // Inizializza il parametro FEI
        console.log('### isLWCModalOpenedPre: ' + this.isLWCModalOpened);
        this.isLWCModalOpened = true;
        console.log('### isLWCModalOpenedPost: ' + this.isLWCModalOpened);
    }
    

    // Restituisce una stringa JSON con i parametri url-encodati
    parseQueryString(queryString) {
        const params = {};
        if (!queryString) return JSON.stringify(params);
        queryString.split('&').forEach(pair => {
            const idx = pair.indexOf('=');
            if (idx > -1) {
                const key = pair.substring(0, idx);
                const value = pair.substring(idx + 1);
                params[key] = encodeURIComponent(value);
            } else {
                params[pair] = '';
            }
        });
        return JSON.stringify(params);
    }
}