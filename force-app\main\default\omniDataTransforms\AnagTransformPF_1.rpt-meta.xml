<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <description><PERSON><PERSON>: 1387195 fix VOC KPI field key</description>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;Key&quot; : {
    &quot;AccountKey&quot; : { },
    &quot;AccountKeyAgency&quot; : { }
  },
  &quot;Value&quot; : {
    &quot;accountDetail_compagnia&quot; : &quot;&quot;,
    &quot;sinistri&quot; : &quot;ND&quot;,
    &quot;accountDetail_originator&quot; : &quot;-&quot;,
    &quot;accountDetail_occupazione&quot; : &quot;-&quot;,
    &quot;user_name&quot; : &quot;<PERSON>&quot;,
    &quot;accountDetail_pagamento&quo<PERSON>; : &quot;[pagamento]&quot;,
    &quot;accountDetail_residenza&quot; : &quot;-&quot;,
    &quot;accountDetailMDM_emailStatus&quot; : &quot;-&quot;,
    &quot;account_cf&quot; : &quot;****************&quot;,
    &quot;contenzioso&quot; : &quot;ND&quot;,
    &quot;accountDetailMDM_Mobile&quot; : &quot;-&quot;,
    &quot;agenzia_Id&quot; : &quot;0019X00000sfj5iQAA&quot;,
    &quot;accountDetail_SourceSystemConsentCode&quot; : &quot;Mancante&quot;,
    &quot;user_id&quot; : &quot;0059X00000PrV0GQAV&quot;,
    &quot;accountDetail_fea&quot; : &quot;No&quot;,
    &quot;compagnia_Id&quot; : &quot;0019X00000sU898QAC&quot;,
    &quot;account_socOrig&quot; : &quot;Unipol&quot;,
    &quot;accountAgencyDetail_mobile&quot; : &quot;-&quot;,
    &quot;accountDetailMDM_Email&quot; : &quot;-&quot;,
    &quot;accountDetail_ciu&quot; : &quot;&quot;,
    &quot;accountDetail_ProfessioneDescrizione&quot; : &quot;-&quot;,
    &quot;accountAgencyDetail_email&quot; : &quot;-&quot;,
    &quot;accountAgencyDetail_clienteAltraAgenzia&quot; : &quot;NO&quot;,
    &quot;accountDetail_isGroupMember&quot; : &quot;-&quot;
  },
  &quot;Consensi&quot; : {
    &quot;DataAggiornamento&quot; : &quot;09-06-2025&quot;,
    &quot;ciu&quot; : null,
    &quot;compagnia&quot; : null,
    &quot;Checkbox&quot; : {
      &quot;error&quot; : &quot;XML or JSON parsing error.&quot;
    },
    &quot;DataInizio&quot; : &quot; - &quot;,
    &quot;TipoConsenso&quot; : &quot;-&quot;,
    &quot;StatoPrivacy&quot; : &quot;Mancante&quot;
  },
  &quot;Nuclei&quot; : {
    &quot;finalResp&quot; : [ {
      &quot;AccountId&quot; : null,
      &quot;name&quot; : &quot;GALLI ADRIANO&quot;,
      &quot;role&quot; : &quot;CAPOFAMIGLIA&quot;,
      &quot;id&quot; : &quot;****************&quot;,
      &quot;groupingId&quot; : &quot;803046300619868801&quot;,
      &quot;cf&quot; : &quot;****************&quot;,
      &quot;birthDate&quot; : -************,
      &quot;address&quot; : &quot;VIA GOI CESARE 61 GRONTARDO CR&quot;,
      &quot;phone&quot; : &quot;**********&quot;,
      &quot;personType&quot; : &quot;P&quot;,
      &quot;LoopBlockIterationIndex&quot; : 1,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;getAccountIdFromCFStatus&quot; : true
    }, {
      &quot;AccountId&quot; : null,
      &quot;name&quot; : &quot;GALLI JURI&quot;,
      &quot;role&quot; : &quot;FAMIGLIARE&quot;,
      &quot;id&quot; : &quot;****************&quot;,
      &quot;groupingId&quot; : &quot;803046300619868801&quot;,
      &quot;cf&quot; : &quot;****************&quot;,
      &quot;birthDate&quot; : ************,
      &quot;address&quot; : &quot;VIA VOLONTARI SOLIDARIETA&apos; 20 GRONTARDO CR&quot;,
      &quot;phone&quot; : &quot;**********&quot;,
      &quot;personType&quot; : &quot;P&quot;,
      &quot;LoopBlockIterationIndex&quot; : 2,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;getAccountIdFromCFStatus&quot; : true
    } ]
  },
  &quot;Convenzioni&quot; : {
    &quot;conv&quot; : null
  },
  &quot;Abbinato&quot; : false,
  &quot;CounterConvenzioni&quot; : 0
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>AnagTransformPF</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <defaultValue>NO</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item3</globalKey>
        <inputFieldName>isAppartenenzaNucleo:isAppartenenzaNucleoVar</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:AppartenenzaNucleo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item2</globalKey>
        <inputFieldName>Value:accountDetail_fea</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:Adesione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value &apos;Cliente&apos; == var:Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value | var:Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value &apos;Non/\/\/contraente&apos; == var:Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value &apos;Prospect&apos; IF IF</formulaConverted>
        <formulaExpression>IF(Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value == &apos;Cliente&apos;, Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value, IF(Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value == &apos;Non contraente&apos;, Key:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value, &apos;Prospect&apos;))</formulaExpression>
        <formulaResultPath>statoSoggetto</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>var:Abbinato</formulaConverted>
        <formulaExpression>Abbinato</formulaExpression>
        <formulaResultPath>abbinatoformula</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item41</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:VoC</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item40</globalKey>
        <inputFieldName>Key:AccountKey:TPD_CANALEACCESSO:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:ModUltimoAccesso:CanaleAccesso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;TPD_APP&quot; : &quot;App&quot;,
  &quot;TPD_WEB&quot; : &quot;Web&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item39</globalKey>
        <inputFieldName>Value:accountDetailMDM_mobileStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:StatoCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>--</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item38</globalKey>
        <inputFieldName>Key:AccountKey:BPER_TUTELASALVAGUARDIABPER:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>RappBancari:TutelaBPER</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item44</globalKey>
        <inputFieldName>Key:AccountKey:TPD_PRESENZAAPP:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:ModUltimoAccesso:PresenzaApp</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;false&quot; : &quot;No&quot;,
  &quot;true&quot; : &quot;Si&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item43</globalKey>
        <inputFieldName>Key:AccountKey:PP_TITOLARITACLIENTE:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:TitolaritaCliente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item42</globalKey>
        <inputFieldName>Key:AccountKey:TPD_COMPORTAMENTOOMNICANALE:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:ModUltimoAccesso:ComportamentoOmnicanale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item33</globalKey>
        <inputFieldName>Value:accountDetail_originator</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Originator</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;1&quot; : &quot;Unipol Assicurazioni S.p.A&quot;,
  &quot;2&quot; : &quot;BIM&quot;,
  &quot;4&quot; : &quot;UNISALUTE&quot;,
  &quot;5&quot; : &quot;LINEAR&quot;,
  &quot;7&quot; : &quot;FATTORIE DEL CERRO&quot;,
  &quot;8&quot; : &quot;LINEAR LIFE&quot;,
  &quot;10&quot; : &quot;ALG&quot;,
  &quot;11&quot; : &quot;NOVAAEG&quot;,
  &quot;12&quot; : &quot;BPER&quot;,
  &quot;13&quot; : &quot;TIM&quot;,
  &quot;14&quot; : &quot;SANTINI&quot;,
  &quot;15&quot; : &quot;CONAD&quot;,
  &quot;16&quot; : &quot;FINITALIA&quot;,
  &quot;17&quot; : &quot;UNIPOLTECH&quot;,
  &quot;18&quot; : &quot;UNA_NAXOS&quot;,
  &quot;19&quot; : &quot;CRIF/CRIBIS&quot;,
  &quot;20&quot; : &quot;PRONTO ASSISTANCE&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item32</globalKey>
        <inputFieldName>Value:accountDetail_fea</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:AdesioneFEA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item31</globalKey>
        <inputFieldName>Nuclei</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>Nuclei</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item30</globalKey>
        <inputFieldName>Consensi:StatoPrivacy</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ConsensoPrivacy</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item37</globalKey>
        <inputFieldName>Value:accountDetailMDM_emailStatus</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:StatoEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item36</globalKey>
        <inputFieldName>Abbinato</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>RappBancari:IsAbbinato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item35</globalKey>
        <inputFieldName>Value:companyForModaleFea</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Anagrafica:ModaleFea:companyForModaleFea</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item34</globalKey>
        <inputFieldName>Value:asset_statoSoggetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:StatoSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item25</globalKey>
        <inputFieldName>Value:accountDetail_ProfessioneDescrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Professione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item24</globalKey>
        <inputFieldName>Value:accountDetailMDM_Email</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item23</globalKey>
        <inputFieldName>Key:AccountKey:BANK_CLIENTEBPER.Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>RappBancari:SegnalazioneBanche</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item22</globalKey>
        <inputFieldName>Value:accountDetail_compagnia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:Compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item29</globalKey>
        <inputFieldName>Value:accountAgencyDetail_email</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Email_Agency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item28</globalKey>
        <inputFieldName>Convenzioni:conv</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Convenzioni:conv</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item27</globalKey>
        <inputFieldName>Abbinato</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>Anagrafica:IsAbbinato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item26</globalKey>
        <inputFieldName>Value:accountAgencyDetail_mobile</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Cellulare_Agency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item21</globalKey>
        <inputFieldName>CounterConvenzioni</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Convenzioni:counter</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item20</globalKey>
        <inputFieldName>Value:accountDetail_ciu</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:Ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item19</globalKey>
        <inputFieldName>Value:account_cf</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:CF</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>--</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item15</globalKey>
        <inputFieldName>Key:AccountKey:BPER_MARCHIATURASOGGETTO:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>RappBancari:MarchiaturaSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item14</globalKey>
        <inputFieldName>Value:accountDetail_feaCellulare</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:Cellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item13</globalKey>
        <inputFieldName>Key:AccountKey:RPO</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:RPO</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item12</globalKey>
        <inputFieldName>Value:accountDetail_residenza</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Residenza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item18</globalKey>
        <inputFieldName>Value:accountDetail_Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>AccountDetailId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item17</globalKey>
        <inputFieldName>Value:gestoreAnagrafica</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:GestoreAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>Visualizza</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item16</globalKey>
        <inputFieldName>Value:accountDetail_pagamento</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:MetodiPagamento</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item7</globalKey>
        <inputFieldName>Value:accountDetail_feaEmail</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:Email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item45</globalKey>
        <inputFieldName>Value:account_eta</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Eta</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item6</globalKey>
        <inputFieldName>Key:AccountKey:CRMA_VALUS:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:Valus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>--</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item5</globalKey>
        <inputFieldName>Value:accountAccountRelationSociey_Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>RappBancari:AccountAccountRelationId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item4</globalKey>
        <inputFieldName>Value:accountDetail_feaDataInizio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ModaleFea:DataInizio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item11</globalKey>
        <inputFieldName>Key:AccountKey:CRMA_CAPACITADISPESA:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:CapacitaDiSpesa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item10</globalKey>
        <inputFieldName>Value:accountDetailMDM_Mobile</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:Cellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item48</globalKey>
        <inputFieldName>statoSoggetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:StatoSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>Non effettuato</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item9</globalKey>
        <inputFieldName>Key:AccountKey:TPD_DATAULTIMOACCESSO:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:UltimoAccesso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item47</globalKey>
        <inputFieldName>Value:accountAgencyDetail_clienteAltraAgenzia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Anagrafica:ClienteAltraAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item8</globalKey>
        <inputFieldName>Key:AccountKeyAgency:CRM_CLIENTE_VIP:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:ClienteVip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagTransformPFCustom0jI9O000000vACLUA2Item46</globalKey>
        <inputFieldName>Key:AccountKeyAgency:MDM_CONTATTABILITA:Value</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagTransformPF</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>KPI:Contattabilita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;Key&quot; : {
    &quot;AccountKey&quot; : { },
    &quot;AccountKeyAgency&quot; : { }
  },
  &quot;Value&quot; : {
    &quot;accountDetail_compagnia&quot; : &quot;&quot;,
    &quot;sinistri&quot; : &quot;ND&quot;,
    &quot;accountDetail_originator&quot; : &quot;-&quot;,
    &quot;accountDetail_occupazione&quot; : &quot;-&quot;,
    &quot;user_name&quot; : &quot;Lorenzo Scrufari&quot;,
    &quot;accountDetail_pagamento&quot; : &quot;[pagamento]&quot;,
    &quot;accountDetail_residenza&quot; : &quot;-&quot;,
    &quot;accountDetailMDM_emailStatus&quot; : &quot;-&quot;,
    &quot;account_cf&quot; : &quot;****************&quot;,
    &quot;contenzioso&quot; : &quot;ND&quot;,
    &quot;accountDetailMDM_Mobile&quot; : &quot;-&quot;,
    &quot;agenzia_Id&quot; : &quot;0019X00000sfj5iQAA&quot;,
    &quot;accountDetail_SourceSystemConsentCode&quot; : &quot;Mancante&quot;,
    &quot;user_id&quot; : &quot;0059X00000PrV0GQAV&quot;,
    &quot;accountDetail_fea&quot; : &quot;No&quot;,
    &quot;compagnia_Id&quot; : &quot;0019X00000sU898QAC&quot;,
    &quot;account_socOrig&quot; : &quot;Unipol&quot;,
    &quot;accountAgencyDetail_mobile&quot; : &quot;-&quot;,
    &quot;accountDetailMDM_Email&quot; : &quot;-&quot;,
    &quot;accountDetail_ciu&quot; : &quot;&quot;,
    &quot;accountDetail_ProfessioneDescrizione&quot; : &quot;-&quot;,
    &quot;accountAgencyDetail_email&quot; : &quot;-&quot;,
    &quot;accountAgencyDetail_clienteAltraAgenzia&quot; : &quot;NO&quot;,
    &quot;accountDetail_isGroupMember&quot; : &quot;-&quot;
  },
  &quot;Consensi&quot; : {
    &quot;DataAggiornamento&quot; : &quot;09-06-2025&quot;,
    &quot;ciu&quot; : null,
    &quot;compagnia&quot; : null,
    &quot;Checkbox&quot; : {
      &quot;error&quot; : &quot;XML or JSON parsing error.&quot;
    },
    &quot;DataInizio&quot; : &quot; - &quot;,
    &quot;TipoConsenso&quot; : &quot;-&quot;,
    &quot;StatoPrivacy&quot; : &quot;Mancante&quot;
  },
  &quot;Nuclei&quot; : {
    &quot;finalResp&quot; : [ {
      &quot;AccountId&quot; : null,
      &quot;name&quot; : &quot;GALLI ADRIANO&quot;,
      &quot;role&quot; : &quot;CAPOFAMIGLIA&quot;,
      &quot;id&quot; : &quot;****************&quot;,
      &quot;groupingId&quot; : &quot;803046300619868801&quot;,
      &quot;cf&quot; : &quot;****************&quot;,
      &quot;birthDate&quot; : -************,
      &quot;address&quot; : &quot;VIA GOI CESARE 61 GRONTARDO CR&quot;,
      &quot;phone&quot; : &quot;**********&quot;,
      &quot;personType&quot; : &quot;P&quot;,
      &quot;LoopBlockIterationIndex&quot; : 1,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;getAccountIdFromCFStatus&quot; : true
    }, {
      &quot;AccountId&quot; : null,
      &quot;name&quot; : &quot;GALLI JURI&quot;,
      &quot;role&quot; : &quot;FAMIGLIARE&quot;,
      &quot;id&quot; : &quot;****************&quot;,
      &quot;groupingId&quot; : &quot;803046300619868801&quot;,
      &quot;cf&quot; : &quot;****************&quot;,
      &quot;birthDate&quot; : ************,
      &quot;address&quot; : &quot;VIA VOLONTARI SOLIDARIETA&apos; 20 GRONTARDO CR&quot;,
      &quot;phone&quot; : &quot;**********&quot;,
      &quot;personType&quot; : &quot;P&quot;,
      &quot;LoopBlockIterationIndex&quot; : 2,
      &quot;LoopBlockIterationStatus&quot; : true,
      &quot;getAccountIdFromCFStatus&quot; : true
    } ]
  },
  &quot;Convenzioni&quot; : {
    &quot;conv&quot; : null
  },
  &quot;Abbinato&quot; : false,
  &quot;CounterConvenzioni&quot; : 0
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>AnagTransformPF_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
