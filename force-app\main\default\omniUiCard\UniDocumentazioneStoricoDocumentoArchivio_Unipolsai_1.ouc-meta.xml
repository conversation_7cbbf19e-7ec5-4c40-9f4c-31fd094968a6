<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDocumentale_RecuperoStoricoArchivio&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;compagnia&quot;:&quot;{compagnia}&quot;,&quot;ciu&quot;:&quot;{ciu}&quot;,&quot;activeUserId&quot;:&quot;{activeUserId}&quot;,&quot;accountDetailsId&quot;:&quot;{accountDetailsId}&quot;,&quot;tipoDocumento&quot;:&quot;{tipoDocumento}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;compagnia\&quot;:\&quot;{compagnia}\&quot;,\&quot;ciu\&quot;:\&quot;{ciu}\&quot;,\&quot;activeUserId\&quot;:\&quot;{activeUserId}\&quot;,\&quot;accountDetailsId\&quot;:\&quot;{accountDetailsId}\&quot;,\&quot;tipoDocumento\&quot;:\&quot;{tipoDocumento}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;ciu&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;name&quot;:&quot;activeUserId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:7},{&quot;name&quot;:&quot;accountDetailsId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;tipoDocumento&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:9}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniDocumentazioneStoricoDocumentoArchivio</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card slds-border_top slds-p-around_x-small &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_top&quot;,&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#8C8181&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #8C8181 1px solid; \n         &quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%7BfilteredList.datiStoricizzati.numeroDocumento%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;}},&quot;elementLabel&quot;:&quot;Text-0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%7BfilteredList.datiStoricizzati.enteRilascio%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;}},&quot;elementLabel&quot;:&quot;Text-0-clone-0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%7BfilteredList.dataInizioValidita%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_4-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;}},&quot;elementLabel&quot;:&quot;Text-1-clone-0&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDocumentale_RecuperoStoricoArchivio&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;compagnia&quot;:&quot;{compagnia}&quot;,&quot;ciu&quot;:&quot;{ciu}&quot;,&quot;activeUserId&quot;:&quot;{activeUserId}&quot;,&quot;accountDetailsId&quot;:&quot;{accountDetailsId}&quot;,&quot;tipoDocumento&quot;:&quot;{tipoDocumento}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;compagnia\&quot;:\&quot;{compagnia}\&quot;,\&quot;ciu\&quot;:\&quot;{ciu}\&quot;,\&quot;activeUserId\&quot;:\&quot;{activeUserId}\&quot;,\&quot;accountDetailsId\&quot;:\&quot;{accountDetailsId}\&quot;,\&quot;tipoDocumento\&quot;:\&quot;{tipoDocumento}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;ciu&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;name&quot;:&quot;activeUserId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:7},{&quot;name&quot;:&quot;accountDetailsId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;tipoDocumento&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:9}]},&quot;title&quot;:&quot; UniDocumentazioneStoricoDocumentoArchivio&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;UniDocumentazioneStoricoDocumentoArchivio&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56}}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;filteredList&quot;:[],&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;error&quot;:&quot;OK&quot;}</sampleDataSourceResponse>
    <versionNumber>1</versionNumber>
</OmniUiCard>
