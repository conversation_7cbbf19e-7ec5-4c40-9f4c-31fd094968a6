<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;DataMapperTransformAction1&quot; : null,
    &quot;TryCatchBlock1&quot; : null,
    &quot;ResponseAction2&quot; : null,
    &quot;ResponseAction1&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null,
  &quot;TryCatchBlock1&quot; : { }
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <name>testcheck</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>testcheckCustom0jI9O000000v8orUAAItem0</globalKey>
        <inputFieldName>BrandMatrixDecision|1:Brand_Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>testcheck</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>BrandName</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>testcheckCustom0jI9O000000v8orUAAItem1</globalKey>
        <inputFieldName>ModelMatrixDecision|1:Model_Number</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>testcheck</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>ModelNumber</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;vlcTimeTracking&quot; : {
    &quot;DataMapperTransformAction1&quot; : null,
    &quot;TryCatchBlock1&quot; : null,
    &quot;ResponseAction2&quot; : null,
    &quot;ResponseAction1&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null,
  &quot;TryCatchBlock1&quot; : { }
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>testcheck_2</uniqueName>
    <versionNumber>2.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
