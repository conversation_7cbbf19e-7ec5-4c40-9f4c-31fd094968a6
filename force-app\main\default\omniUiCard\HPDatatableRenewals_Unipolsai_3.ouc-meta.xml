<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;[\n  {\n    \&quot;empty\&quot;: false,\n    \&quot;label\&quot;: \&quot;Incassati\&quot;,\n    \&quot;premio\&quot;: \&quot;€1.200.000,00\&quot;,\n    \&quot;numero\&quot;: 800\n  },\n  {\n    \&quot;label\&quot;: \&quot;Da incassare\&quot;,\n    \&quot;premio\&quot;: \&quot;€342.300,50\&quot;,\n    \&quot;numero\&quot;: 250\n  },\n  {\n    \&quot;label\&quot;: \&quot;Totale\&quot;,\n    \&quot;premio\&quot;: \&quot;€1.542.300,50\&quot;,\n    \&quot;numero\&quot;: 1050\n  }\n]&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Parent.period&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;Parent.fromDate&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;Parent.toDate&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;Parent.section&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:7},{&quot;name&quot;:&quot;Parent.rate&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:9},{&quot;name&quot;:&quot;Parent.referent&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:11},{&quot;name&quot;:&quot;Parent.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:34}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>HPDatatableRenewals</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Datatable&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:false,&quot;issortavailable&quot;:true,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;empty&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;cellLevelEdit&quot;:true,&quot;pagelimit&quot;:3,&quot;groupOrder&quot;:&quot;asc&quot;,&quot;searchDatatable&quot;:&quot;&quot;,&quot;columns&quot;:[{&quot;fieldName&quot;:&quot;label&quot;,&quot;label&quot;:&quot;Tipologia&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:&quot;false&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;numero&quot;,&quot;label&quot;:&quot;Numero&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:&quot;false&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;premio&quot;,&quot;label&quot;:&quot;Premio&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:&quot;true&quot;,&quot;type&quot;:&quot;text&quot;}],&quot;records&quot;:&quot;{records}&quot;,&quot;styles&quot;:{&quot;cellMargin&quot;:[],&quot;cellPadding&quot;:[],&quot;cellBgColor&quot;:&quot;&quot;,&quot;cellBorderColor&quot;:&quot;&quot;,&quot;tableBorderColor&quot;:&quot;&quot;,&quot;tableBgColor&quot;:&quot;&quot;,&quot;headBgColor&quot;:&quot;#F2F2F2&quot;,&quot;headTextDecoration&quot;:&quot;&quot;,&quot;headFontFamily&quot;:&quot;&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;1px&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;cellMargin&quot;:[],&quot;cellPadding&quot;:[],&quot;cellBgColor&quot;:&quot;&quot;,&quot;cellBorderColor&quot;:&quot;&quot;,&quot;tableBorderColor&quot;:&quot;&quot;,&quot;tableBgColor&quot;:&quot;&quot;,&quot;headBgColor&quot;:&quot;#F2F2F2&quot;,&quot;headTextDecoration&quot;:&quot;&quot;,&quot;headFontFamily&quot;:&quot;&quot;}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#181818&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left &quot;,&quot;style&quot;:&quot;     border-top: #ccc 1px 1px;border-right: #ccc 1px 1px;border-bottom: #ccc 1px 1px;border-left: #ccc 1px 1px; \n        color:#181818; &quot;,&quot;customClass&quot;:&quot;&quot;},&quot;elementLabel&quot;:&quot;Datatable-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;1px&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;cellMargin&quot;:[],&quot;cellPadding&quot;:[],&quot;cellBgColor&quot;:&quot;&quot;,&quot;cellBorderColor&quot;:&quot;&quot;,&quot;tableBorderColor&quot;:&quot;&quot;,&quot;tableBgColor&quot;:&quot;&quot;,&quot;headBgColor&quot;:&quot;#F2F2F2&quot;,&quot;headTextDecoration&quot;:&quot;&quot;,&quot;headFontFamily&quot;:&quot;&quot;}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#181818&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left &quot;,&quot;style&quot;:&quot;     border-top: #ccc 1px 1px;border-right: #ccc 1px 1px;border-bottom: #ccc 1px 1px;border-left: #ccc 1px 1px; \n        color:#181818; &quot;,&quot;customClass&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;[\n  {\n    \&quot;empty\&quot;: false,\n    \&quot;label\&quot;: \&quot;Incassati\&quot;,\n    \&quot;premio\&quot;: \&quot;€1.200.000,00\&quot;,\n    \&quot;numero\&quot;: 800\n  },\n  {\n    \&quot;label\&quot;: \&quot;Da incassare\&quot;,\n    \&quot;premio\&quot;: \&quot;€342.300,50\&quot;,\n    \&quot;numero\&quot;: 250\n  },\n  {\n    \&quot;label\&quot;: \&quot;Totale\&quot;,\n    \&quot;premio\&quot;: \&quot;€1.542.300,50\&quot;,\n    \&quot;numero\&quot;: 1050\n  }\n]&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Parent.period&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;Parent.fromDate&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;Parent.toDate&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;Parent.section&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:7},{&quot;name&quot;:&quot;Parent.rate&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:9},{&quot;name&quot;:&quot;Parent.referent&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:11},{&quot;name&quot;:&quot;Parent.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:34}]},&quot;title&quot;:&quot;HPDatatableRenewals&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;HPDatatableRenewals&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56},&quot;isRepeatable&quot;:false,&quot;events&quot;:[{&quot;eventname&quot;:&quot;reload&quot;,&quot;channelname&quot;:&quot;HPDatatableRenewals&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753790407483-2a03jbadi&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753790407525&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;HPDatatableRenewals:reload&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}]}</propertySetConfig>
    <sampleDataSourceResponse>[{&quot;empty&quot;:false,&quot;label&quot;:&quot;Incassati&quot;,&quot;premio&quot;:&quot;€1.200.000,00&quot;,&quot;numero&quot;:800},{&quot;label&quot;:&quot;Da incassare&quot;,&quot;premio&quot;:&quot;€342.300,50&quot;,&quot;numero&quot;:250},{&quot;label&quot;:&quot;Totale&quot;,&quot;premio&quot;:&quot;€1.542.300,50&quot;,&quot;numero&quot;:1050}]</sampleDataSourceResponse>
    <versionNumber>3</versionNumber>
</OmniUiCard>
