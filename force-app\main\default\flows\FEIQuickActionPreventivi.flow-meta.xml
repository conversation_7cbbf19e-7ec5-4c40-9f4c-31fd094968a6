<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>SetIPCRUSCOTTODISPATCHpayload</name>
        <label>SetIPCRUSCOTTODISPATCHpayload</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>payloadIPCRUSCOTTODISPATCH</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SetNPACCRUSCOTTODISPATCHpayload</name>
        <label>SetNPACCRUSCOTTODISPATCHpayload</label>
        <locationX>490</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>payloadNPACCRUSCOTTODISPATCH</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SetPayloadVITAPROPOSTA</name>
        <label>SetPayloadVITAPROPOSTA</label>
        <locationX>1370</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>payloadVITAPROPOSTA</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>SetRECRUSCOTTODISPATCHpayload</name>
        <label>SetRECRUSCOTTODISPATCHpayload</label>
        <locationX>930</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>feiRequestPayload</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>payloadRECRUSCOTTODISPATCH</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>FeiContainerScreen</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_FEI_Type</name>
        <label>Check FEI Type</label>
        <locationX>996</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IP</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>IP.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_IP</targetReference>
            </connector>
            <label>IP.</label>
        </rules>
        <rules>
            <name>NPAC</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>NPAC.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_NPAC</targetReference>
            </connector>
            <label>NPAC.</label>
        </rules>
        <rules>
            <name>RE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>RE.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_RE</targetReference>
            </connector>
            <label>RE.</label>
        </rules>
        <rules>
            <name>VITA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>VITA.</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_VITA</targetReference>
            </connector>
            <label>VITA.</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_IP</name>
        <label>Check IP</label>
        <locationX>182</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IP_CRUSCOTTO_DISPATCH</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>IP.CRUSCOTTO.DISPATCH</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SetIPCRUSCOTTODISPATCHpayload</targetReference>
            </connector>
            <label>IP.CRUSCOTTO.DISPATCH</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_NPAC</name>
        <label>Check NPAC</label>
        <locationX>622</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>NPAC_CRUSCOTTO_DISPATCH</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>NPAC.CRUSCOTTO.DISPATCH</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SetNPACCRUSCOTTODISPATCHpayload</targetReference>
            </connector>
            <label>NPAC.CRUSCOTTO.DISPATCH</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_RE</name>
        <label>Check RE</label>
        <locationX>1062</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>RE_CRUSCOTTO_DISPATCH</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RE.CRUSCOTTO.DISPATCH</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SetRECRUSCOTTODISPATCHpayload</targetReference>
            </connector>
            <label>RE.CRUSCOTTO.DISPATCH</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_VITA</name>
        <label>Check VITA</label>
        <locationX>1502</locationX>
        <locationY>458</locationY>
        <defaultConnector>
            <targetReference>FeiContainerScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>VITA_PROPOSTA</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>FEIID</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>VITA.PROPOSTA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SetPayloadVITAPROPOSTA</targetReference>
            </connector>
            <label>VITA.PROPOSTA</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>environment</name>
        <dataType>String</dataType>
        <expression>{!$Setup.FEI_Environment__c.Environment__c}</expression>
    </formulas>
    <formulas>
        <name>payloadIPCRUSCOTTODISPATCH</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;compagnia&quot;:&quot;{$compagnia}&quot;,&quot;agenzia&quot;:&quot;{$agenzia}&quot;,&quot;progressivo&quot;:&quot;1&quot;,&quot;numeroArchivio&quot;:&quot;-&apos;&amp;{!GetQuote.SourceSystemIdentifier0Formula__c}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>payloadNPACCRUSCOTTODISPATCH</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;tipoOperazione&quot;:&quot;DE&quot;,&quot;compagnia&quot;:&quot;{$compagnia}&quot;,&quot;agenzia&quot;:&quot;{$agenzia}&quot;,&quot;progressivo&quot;:&quot;1&quot;,&quot;numeroArchivio&quot;:&quot;-&apos;&amp;{!GetQuote.SourceSystemIdentifier0Formula__c}&amp;&apos;&quot;,&quot;numeroVersione&quot;:&quot;&apos;&amp;{!GetQuote.SourceSystemIdentifier1Formula__c}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>payloadRECRUSCOTTODISPATCH</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;tipoOperazione&quot;:&quot;DEF_AGENZIA&quot;,&quot;compagnia&quot;:&quot;{$compagnia}&quot;,&quot;agenzia&quot;:&quot;{$agenzia}&quot;,&quot;progressivo&quot;:&quot;1&quot;,&quot;numeroArchivio&quot;:&quot;-&apos;&amp;{!GetQuote.SourceSystemIdentifier0Formula__c}&amp;&apos;&quot;,&quot;numeroVersione&quot;:&quot;&apos;&amp;{!GetQuote.SourceSystemIdentifier1Formula__c}&amp;&apos;&quot;}&apos;</expression>
    </formulas>
    <formulas>
        <name>payloadVITAPROPOSTA</name>
        <dataType>String</dataType>
        <expression>&apos;{&quot;chiamante&quot;:&quot;WKCS&quot;,&quot;funzione&quot;:&quot;5&quot;,&quot;proposta&quot;:&quot;&apos; &amp;{!GetQuote.SourceSystemIdentifier0Formula__c}&amp; &apos;&quot;}&apos;</expression>
    </formulas>
    <interviewLabel>FEIQuickActionByQuote {!$Flow.CurrentDateTime}</interviewLabel>
    <label>FEIQuickActionPreventivi</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>GetCurrentFEISetting</name>
        <label>GetCurrentFEISetting</label>
        <locationX>996</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_FEI_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Label</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>FEIID</elementReference>
            </value>
        </filters>
        <filters>
            <field>Environment__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>environment</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>FEI_Settings__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetQuote</name>
        <label>GetQuote</label>
        <locationX>996</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetCurrentFEISetting</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Quote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>FeiContainerScreen</name>
        <label>FeiContainerScreen</label>
        <locationX>996</locationX>
        <locationY>842</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>FeiContainer</name>
            <extensionName>c:feiContainer</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>recordId</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>feiRequestPayload</name>
                <value>
                    <elementReference>feiRequestPayload</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>FEIID</name>
                <value>
                    <elementReference>FEIID</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>FiscalCode</name>
                <value>
                    <elementReference>$User.FederationIdentifier</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>permissionSetName</name>
                <value>
                    <elementReference>GetCurrentFEISetting.UCA_Permission_Name__c</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>society</name>
                <value>
                    <elementReference>society</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>ResetValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>870</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetQuote</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>FEIID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>feiRequestPayload</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>society</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
