<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>childflyout/Unipolsai/2.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDocumentale_RecuperoStorico&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;compagnia&quot;:&quot;{nomeCompagnia}&quot;,&quot;ciu&quot;:&quot;{ciu}&quot;,&quot;idDocumento&quot;:&quot;{idDocumento}&quot;,&quot;activeUserId&quot;:&quot;{activeUserId}&quot;,&quot;accountDetailsId&quot;:&quot;{accountDetailsId}&quot;,&quot;tipoDocumento&quot;:&quot;{tipoDocumento}&quot;,&quot;trigger&quot;:&quot;true&quot;},&quot;jsonMap&quot;:&quot;{\&quot;ciu\&quot;:\&quot;{ciu}\&quot;,\&quot;idDocumento\&quot;:\&quot;{idDocumento}\&quot;,\&quot;nomeCompagnia\&quot;:\&quot;{nomeCompagnia}\&quot;,\&quot;activeUserId\&quot;:\&quot;{activeUserId}\&quot;,\&quot;accountDetailsId\&quot;:\&quot;{accountDetailsId}\&quot;,\&quot;tipoDocumento\&quot;:\&quot;{tipoDocumento}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;ciu&quot;,&quot;val&quot;:&quot;38910&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;idDocumento&quot;,&quot;val&quot;:&quot;9744988&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;nomeCompagnia&quot;,&quot;val&quot;:&quot;unipolsai&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;activeUserId&quot;,&quot;val&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;id&quot;:20},{&quot;name&quot;:&quot;accountDetailsId&quot;,&quot;val&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;id&quot;:22},{&quot;name&quot;:&quot;tipoDocumento&quot;,&quot;val&quot;:&quot;CDN&quot;,&quot;id&quot;:59}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniDocumentazioneStoricoDocumento</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;,&quot;label&quot;:&quot;around:none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-around_none &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;      \n         &quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;&quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n        color:#000; &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_left%22%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3EDOCUMENTO%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;records-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_0_0_block_1_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_top&quot;,&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#8C8181&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #8C8181 1px solid; \n         &quot;},&quot;children&quot;:[],&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;records-Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_top&quot;,&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#8C8181&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #8C8181 1px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_0_0_outputField_2_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3ETipologia%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;records-Text-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3EData%20Scadenza%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;records-Text-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3ENome%20Compagnia%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;records-Text-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;},{&quot;key&quot;:&quot;element_element_block_0_0_outputField_5_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3ENumero%20Documento%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;records-Text-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;&quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n        color:#000; &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%7Bresults%5B0%5D.datiStoricizzati.tipoDelDocumento%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;,&quot;elementLabel&quot;:&quot;records-Text-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%7Bresults%5B0%5D.datiStoricizzati.dataScadenza%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;records-Text-6&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%7Bresults%5B0%5D.compagnia%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;records-Text-8&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;},{&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_3_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%7Bresults%5B0%5D.datiStoricizzati.numeroDocumento%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;,&quot;elementLabel&quot;:&quot;records-Text-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3EStato%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;records-Text-5-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_4_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3EProvincia%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;Block-0-clone-0-Text-4-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_5_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3EComune%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;Block-0-clone-0-Text-5-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_6_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3EAutorita%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;Block-0-clone-0-Text-6-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_7_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%7Bnazione%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;Block-0-clone-0-Text-3-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_8_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%7Bresults%5B0%5D.datiStoricizzati.codiceProvincia%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;Block-0-clone-0-Text-8-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_9_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%7Bcomune%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;Block-0-clone-0-Text-9-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_10_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%7Bente%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;Block-0-clone-0-Text-10-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;around:xx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_xx-small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_0_0_block_6_0_outputField_11_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_0_0_block_6_0&quot;}],&quot;elementLabel&quot;:&quot;Block-0-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;&quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n        color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;userUpdatedElementLabel&quot;:true,&quot;key&quot;:&quot;element_element_block_0_0_block_6_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;}],&quot;elementLabel&quot;:&quot;records&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;class&quot;:&quot;&quot;,&quot;sizeClass&quot;:&quot;slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:true,&quot;default&quot;:&quot;12&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n        color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;userUpdatedElementLabel&quot;:true},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_left%22%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3EARCHIVIO%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;      \n         &quot;},&quot;elementLabel&quot;:&quot;records-Text-0-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_top&quot;,&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#8C8181&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #8C8181 1px solid; \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3ENumero%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_4-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;Block-0-clone-0-Text-4-clone-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_4-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_outputField_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3EAutorita%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_4-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;records-Block-1-clone-0-Text-0-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_4-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cstrong%3EValidita%20Documento%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_4-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;elementLabel&quot;:&quot;records-Block-1-clone-0-Text-1-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_4-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;around:small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;4&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_bottom&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;0&quot;,&quot;color&quot;:&quot;#000000&quot;,&quot;radius&quot;:&quot;0&quot;,&quot;style&quot;:&quot;solid&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;#000&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_small &quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #000000 0px solid;border-right: #000000 0px solid;border-bottom: #000000 0px solid;border-left: #000000 0px solid; \n    border-radius:0;    color:#000; &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_2_0_outputField_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;}],&quot;elementLabel&quot;:&quot;records-Block-1-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#e5e5e5&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_top&quot;,&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#8C8181&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#e5e5e5;     border-top: #8C8181 1px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDocumentale_RecuperoStorico&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;compagnia&quot;:&quot;{nomeCompagnia}&quot;,&quot;ciu&quot;:&quot;{ciu}&quot;,&quot;idDocumento&quot;:&quot;{idDocumento}&quot;,&quot;activeUserId&quot;:&quot;{activeUserId}&quot;,&quot;accountDetailsId&quot;:&quot;{accountDetailsId}&quot;,&quot;tipoDocumento&quot;:&quot;{tipoDocumento}&quot;,&quot;trigger&quot;:&quot;true&quot;},&quot;jsonMap&quot;:&quot;{\&quot;ciu\&quot;:\&quot;{ciu}\&quot;,\&quot;idDocumento\&quot;:\&quot;{idDocumento}\&quot;,\&quot;nomeCompagnia\&quot;:\&quot;{nomeCompagnia}\&quot;,\&quot;activeUserId\&quot;:\&quot;{activeUserId}\&quot;,\&quot;accountDetailsId\&quot;:\&quot;{accountDetailsId}\&quot;,\&quot;tipoDocumento\&quot;:\&quot;{tipoDocumento}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;ciu&quot;,&quot;val&quot;:&quot;38910&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;idDocumento&quot;,&quot;val&quot;:&quot;9744988&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;nomeCompagnia&quot;,&quot;val&quot;:&quot;unipolsai&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;activeUserId&quot;,&quot;val&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;id&quot;:20},{&quot;name&quot;:&quot;accountDetailsId&quot;,&quot;val&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;id&quot;:22},{&quot;name&quot;:&quot;tipoDocumento&quot;,&quot;val&quot;:&quot;CDN&quot;,&quot;id&quot;:59}]},&quot;title&quot;:&quot;UniDocumentazioneStoricoDocumento&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfConfiguratoreProfiliRow_1_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003ASu9SAG&quot;,&quot;MasterLabel&quot;:&quot;cfConfiguratoreProfiliRow_1_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;UniDocumentazioneStoricoDocumento&quot;,&quot;apiVersion&quot;:56,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;]},&quot;isExplicitImport&quot;:false},&quot;uniqueKey&quot;:&quot;&quot;,&quot;isRepeatable&quot;:false}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;response&quot;:{&quot;timestamp&quot;:&quot;2025-08-01T14:24:52.804764425Z&quot;,&quot;correlationId&quot;:&quot;6080f24f-621d-4674-aa5d-69bee3b6311f&quot;,&quot;error&quot;:{&quot;errorPayload&quot;:{&quot;response&quot;:&quot;Unexpected Error&quot;},&quot;errorMessage&quot;:&quot;Invalid value &apos;-&apos; for query parameter idDocumento. expected type: Number, found: String&quot;,&quot;errorStatus&quot;:&quot;APIKIT:BAD_REQUEST&quot;,&quot;errorDescription&quot;:&quot;Invalid value &apos;-&apos; for query parameter idDocumento. expected type: Number, found: String&quot;},&quot;status&quot;:400},&quot;ResponseActionStatus&quot;:true,&quot;SetResponseStatus&quot;:true,&quot;GetEnte&quot;:[],&quot;GetEnteStatus&quot;:true,&quot;GetStato&quot;:{&quot;options&quot;:[{&quot;value&quot;:&quot;Z200&quot;,&quot;label&quot;:&quot;AFGHANISTAN&quot;},{&quot;value&quot;:&quot;Z100&quot;,&quot;label&quot;:&quot;ALBANIA&quot;},{&quot;value&quot;:&quot;Z301&quot;,&quot;label&quot;:&quot;ALGERIA&quot;},{&quot;value&quot;:&quot;Z725&quot;,&quot;label&quot;:&quot;AMERICAN SAMOA&quot;},{&quot;value&quot;:&quot;Z101&quot;,&quot;label&quot;:&quot;ANDORRA&quot;},{&quot;value&quot;:&quot;Z302&quot;,&quot;label&quot;:&quot;ANGOLA&quot;},{&quot;value&quot;:&quot;Z529&quot;,&quot;label&quot;:&quot;ANGUILLA&quot;},{&quot;value&quot;:&quot;Z532&quot;,&quot;label&quot;:&quot;ANTIGUA E BARBUDA&quot;},{&quot;value&quot;:&quot;Z203&quot;,&quot;label&quot;:&quot;ARABIA SAUDITA&quot;},{&quot;value&quot;:&quot;Z600&quot;,&quot;label&quot;:&quot;ARGENTINA&quot;},{&quot;value&quot;:&quot;Z252&quot;,&quot;label&quot;:&quot;ARMENIA&quot;},{&quot;value&quot;:&quot;Z501&quot;,&quot;label&quot;:&quot;ARUBA&quot;},{&quot;value&quot;:&quot;Z700&quot;,&quot;label&quot;:&quot;AUSTRALIA&quot;},{&quot;value&quot;:&quot;Z102&quot;,&quot;label&quot;:&quot;AUSTRIA&quot;},{&quot;value&quot;:&quot;Z253&quot;,&quot;label&quot;:&quot;AZERBAIJAN&quot;},{&quot;value&quot;:&quot;Z502&quot;,&quot;label&quot;:&quot;BAHAMAS&quot;},{&quot;value&quot;:&quot;Z204&quot;,&quot;label&quot;:&quot;BAHREIN&quot;},{&quot;value&quot;:&quot;Z249&quot;,&quot;label&quot;:&quot;BANGLADESH&quot;},{&quot;value&quot;:&quot;Z522&quot;,&quot;label&quot;:&quot;BARBADOS&quot;},{&quot;value&quot;:&quot;Z103&quot;,&quot;label&quot;:&quot;BELGIO&quot;},{&quot;value&quot;:&quot;Z512&quot;,&quot;label&quot;:&quot;BELIZE&quot;},{&quot;value&quot;:&quot;Z314&quot;,&quot;label&quot;:&quot;BENIN&quot;},{&quot;value&quot;:&quot;Z400&quot;,&quot;label&quot;:&quot;BERMUDA&quot;},{&quot;value&quot;:&quot;Z205&quot;,&quot;label&quot;:&quot;BHUTAN&quot;},{&quot;value&quot;:&quot;Z139&quot;,&quot;label&quot;:&quot;BIELORUSSIA&quot;},{&quot;value&quot;:&quot;Z601&quot;,&quot;label&quot;:&quot;BOLIVIA&quot;},{&quot;value&quot;:&quot;Z153&quot;,&quot;label&quot;:&quot;BOSNIA ED ERZEGOVINA&quot;},{&quot;value&quot;:&quot;Z358&quot;,&quot;label&quot;:&quot;BOTSWANA&quot;},{&quot;value&quot;:&quot;Z602&quot;,&quot;label&quot;:&quot;BRASILE&quot;},{&quot;value&quot;:&quot;Z104&quot;,&quot;label&quot;:&quot;BULGARIA&quot;},{&quot;value&quot;:&quot;Z354&quot;,&quot;label&quot;:&quot;BURKINA FASO&quot;},{&quot;value&quot;:&quot;Z305&quot;,&quot;label&quot;:&quot;BURUNDI&quot;},{&quot;value&quot;:&quot;Z716&quot;,&quot;label&quot;:&quot;CALEDONIA NUOVA&quot;},{&quot;value&quot;:&quot;Z208&quot;,&quot;label&quot;:&quot;CAMBOGIA&quot;},{&quot;value&quot;:&quot;Z306&quot;,&quot;label&quot;:&quot;CAMERUN&quot;},{&quot;value&quot;:&quot;Z401&quot;,&quot;label&quot;:&quot;CANADA&quot;},{&quot;value&quot;:&quot;Z307&quot;,&quot;label&quot;:&quot;CAPO VERDE&quot;},{&quot;value&quot;:&quot;Z309&quot;,&quot;label&quot;:&quot;CIAD&quot;},{&quot;value&quot;:&quot;Z603&quot;,&quot;label&quot;:&quot;CILE&quot;},{&quot;value&quot;:&quot;Z210&quot;,&quot;label&quot;:&quot;CINA REPUBBLICA POPOLARE&quot;},{&quot;value&quot;:&quot;Z211&quot;,&quot;label&quot;:&quot;CIPRO&quot;},{&quot;value&quot;:&quot;Z106&quot;,&quot;label&quot;:&quot;CITTA&apos; DEL VATICANO&quot;},{&quot;value&quot;:&quot;Z604&quot;,&quot;label&quot;:&quot;COLOMBIA&quot;},{&quot;value&quot;:&quot;Z310&quot;,&quot;label&quot;:&quot;COMORE&quot;},{&quot;value&quot;:&quot;Z311&quot;,&quot;label&quot;:&quot;CONGO REPUBBLICA&quot;},{&quot;value&quot;:&quot;Z312&quot;,&quot;label&quot;:&quot;CONGO REPUBBLICA DEMOCRATICA&quot;},{&quot;value&quot;:&quot;Z214&quot;,&quot;label&quot;:&quot;COREA DEL NORD&quot;},{&quot;value&quot;:&quot;Z213&quot;,&quot;label&quot;:&quot;COREA DEL SUD&quot;},{&quot;value&quot;:&quot;Z313&quot;,&quot;label&quot;:&quot;COSTA D&apos;AVORIO&quot;},{&quot;value&quot;:&quot;Z503&quot;,&quot;label&quot;:&quot;COSTA RICA&quot;},{&quot;value&quot;:&quot;Z149&quot;,&quot;label&quot;:&quot;CROAZIA&quot;},{&quot;value&quot;:&quot;Z504&quot;,&quot;label&quot;:&quot;CUBA&quot;},{&quot;value&quot;:&quot;Z107&quot;,&quot;label&quot;:&quot;DANIMARCA&quot;},{&quot;value&quot;:&quot;Z900&quot;,&quot;label&quot;:&quot;DIPENDENZE AUSTRALIANE&quot;},{&quot;value&quot;:&quot;Z901&quot;,&quot;label&quot;:&quot;DIPENDENZE BRITANNICHE&quot;},{&quot;value&quot;:&quot;Z800&quot;,&quot;label&quot;:&quot;DIPENDENZE CANADESI&quot;},{&quot;value&quot;:&quot;Z902&quot;,&quot;label&quot;:&quot;DIPENDENZE FRANCESI&quot;},{&quot;value&quot;:&quot;Z903&quot;,&quot;label&quot;:&quot;DIPENDENZE NEOZELANDESI&quot;},{&quot;value&quot;:&quot;Z904&quot;,&quot;label&quot;:&quot;DIPENDENZE NORVEGESI ANTARTICHE&quot;},{&quot;value&quot;:&quot;Z801&quot;,&quot;label&quot;:&quot;DIPENDENZE NORVEGESI ARTICHE&quot;},{&quot;value&quot;:&quot;Z802&quot;,&quot;label&quot;:&quot;DIPENDENZE RUSSE&quot;},{&quot;value&quot;:&quot;Z905&quot;,&quot;label&quot;:&quot;DIPENDENZE STATUNITENSI&quot;},{&quot;value&quot;:&quot;Z906&quot;,&quot;label&quot;:&quot;DIPENDENZE SUDAFRICANE&quot;},{&quot;value&quot;:&quot;Z361&quot;,&quot;label&quot;:&quot;DJIBOUTI&quot;},{&quot;value&quot;:&quot;Z526&quot;,&quot;label&quot;:&quot;DOMINICA&quot;},{&quot;value&quot;:&quot;Z605&quot;,&quot;label&quot;:&quot;ECUADOR&quot;},{&quot;value&quot;:&quot;Z336&quot;,&quot;label&quot;:&quot;EGITTO&quot;},{&quot;value&quot;:&quot;Z506&quot;,&quot;label&quot;:&quot;EL SALVADOR&quot;},{&quot;value&quot;:&quot;Z215&quot;,&quot;label&quot;:&quot;EMIRATI ARABI UNITI&quot;},{&quot;value&quot;:&quot;Z368&quot;,&quot;label&quot;:&quot;ERITREA&quot;},{&quot;value&quot;:&quot;Z144&quot;,&quot;label&quot;:&quot;ESTONIA&quot;},{&quot;value&quot;:&quot;Z349&quot;,&quot;label&quot;:&quot;ESWATINI&quot;},{&quot;value&quot;:&quot;Z315&quot;,&quot;label&quot;:&quot;ETIOPIA&quot;},{&quot;value&quot;:&quot;Z108&quot;,&quot;label&quot;:&quot;FAROE ISLANDS&quot;},{&quot;value&quot;:&quot;Z704&quot;,&quot;label&quot;:&quot;FIJI&quot;},{&quot;value&quot;:&quot;Z216&quot;,&quot;label&quot;:&quot;FILIPPINE&quot;},{&quot;value&quot;:&quot;Z109&quot;,&quot;label&quot;:&quot;FINLANDIA&quot;},{&quot;value&quot;:&quot;Z110&quot;,&quot;label&quot;:&quot;FRANCIA&quot;},{&quot;value&quot;:&quot;Z316&quot;,&quot;label&quot;:&quot;GABON&quot;},{&quot;value&quot;:&quot;Z317&quot;,&quot;label&quot;:&quot;GAMBIA&quot;},{&quot;value&quot;:&quot;Z254&quot;,&quot;label&quot;:&quot;GEORGIA&quot;},{&quot;value&quot;:&quot;Z112&quot;,&quot;label&quot;:&quot;GERMANIA&quot;},{&quot;value&quot;:&quot;Z260&quot;,&quot;label&quot;:&quot;GERUSALEMME&quot;},{&quot;value&quot;:&quot;Z318&quot;,&quot;label&quot;:&quot;GHANA&quot;},{&quot;value&quot;:&quot;Z507&quot;,&quot;label&quot;:&quot;GIAMAICA&quot;},{&quot;value&quot;:&quot;Z219&quot;,&quot;label&quot;:&quot;GIAPPONE&quot;},{&quot;value&quot;:&quot;Z113&quot;,&quot;label&quot;:&quot;GIBILTERRA&quot;},{&quot;value&quot;:&quot;Z220&quot;,&quot;label&quot;:&quot;GIORDANIA&quot;},{&quot;value&quot;:&quot;Z115&quot;,&quot;label&quot;:&quot;GRECIA&quot;},{&quot;value&quot;:&quot;Z524&quot;,&quot;label&quot;:&quot;GRENADA&quot;},{&quot;value&quot;:&quot;Z402&quot;,&quot;label&quot;:&quot;GROENLANDIA&quot;},{&quot;value&quot;:&quot;Z508&quot;,&quot;label&quot;:&quot;GUADALUPE&quot;},{&quot;value&quot;:&quot;Z706&quot;,&quot;label&quot;:&quot;GUAM&quot;},{&quot;value&quot;:&quot;Z509&quot;,&quot;label&quot;:&quot;GUATEMALA&quot;},{&quot;value&quot;:&quot;Z319&quot;,&quot;label&quot;:&quot;GUINEA&quot;},{&quot;value&quot;:&quot;Z321&quot;,&quot;label&quot;:&quot;GUINEA EQUATORIALE&quot;},{&quot;value&quot;:&quot;Z320&quot;,&quot;label&quot;:&quot;GUINEA-BISSAU&quot;},{&quot;value&quot;:&quot;Z606&quot;,&quot;label&quot;:&quot;GUYANA&quot;},{&quot;value&quot;:&quot;Z607&quot;,&quot;label&quot;:&quot;GUYANA FRANCESE&quot;},{&quot;value&quot;:&quot;Z510&quot;,&quot;label&quot;:&quot;HAITI&quot;},{&quot;value&quot;:&quot;Z511&quot;,&quot;label&quot;:&quot;HONDURAS&quot;},{&quot;value&quot;:&quot;Z222&quot;,&quot;label&quot;:&quot;INDIA&quot;},{&quot;value&quot;:&quot;Z223&quot;,&quot;label&quot;:&quot;INDONESIA&quot;},{&quot;value&quot;:&quot;Z224&quot;,&quot;label&quot;:&quot;IRAN&quot;},{&quot;value&quot;:&quot;Z225&quot;,&quot;label&quot;:&quot;IRAQ&quot;},{&quot;value&quot;:&quot;Z707&quot;,&quot;label&quot;:&quot;IRIAN OCCIDENTALE&quot;},{&quot;value&quot;:&quot;Z116&quot;,&quot;label&quot;:&quot;IRLANDA&quot;},{&quot;value&quot;:&quot;Z117&quot;,&quot;label&quot;:&quot;ISLANDA&quot;},{&quot;value&quot;:&quot;Z702&quot;,&quot;label&quot;:&quot;ISOLA CHRISTMAS&quot;},{&quot;value&quot;:&quot;Z715&quot;,&quot;label&quot;:&quot;ISOLA NORFOLK&quot;},{&quot;value&quot;:&quot;Z530&quot;,&quot;label&quot;:&quot;ISOLE CAYMAN&quot;},{&quot;value&quot;:&quot;Z212&quot;,&quot;label&quot;:&quot;ISOLE COCOS&quot;},{&quot;value&quot;:&quot;Z703&quot;,&quot;label&quot;:&quot;ISOLE COOK&quot;},{&quot;value&quot;:&quot;Z609&quot;,&quot;label&quot;:&quot;ISOLE FALKLAND&quot;},{&quot;value&quot;:&quot;Z711&quot;,&quot;label&quot;:&quot;ISOLE MARSHALL&quot;},{&quot;value&quot;:&quot;Z724&quot;,&quot;label&quot;:&quot;ISOLE SOLOMON&quot;},{&quot;value&quot;:&quot;Z519&quot;,&quot;label&quot;:&quot;ISOLE TURKS E CAICOS&quot;},{&quot;value&quot;:&quot;Z520&quot;,&quot;label&quot;:&quot;ISOLE VERGINI AMERICANE&quot;},{&quot;value&quot;:&quot;Z525&quot;,&quot;label&quot;:&quot;ISOLE VERGINI BRITANNICHE&quot;},{&quot;value&quot;:&quot;Z729&quot;,&quot;label&quot;:&quot;ISOLE WALLIS E FUTUNA&quot;},{&quot;value&quot;:&quot;Z226&quot;,&quot;label&quot;:&quot;ISRAELE&quot;},{&quot;value&quot;:&quot;Z000&quot;,&quot;label&quot;:&quot;ITALIA&quot;},{&quot;value&quot;:&quot;Z124&quot;,&quot;label&quot;:&quot;JERSEY (BALIATO DI)&quot;},{&quot;value&quot;:&quot;Z255&quot;,&quot;label&quot;:&quot;KAZAKHSTAN&quot;},{&quot;value&quot;:&quot;Z322&quot;,&quot;label&quot;:&quot;KENYA&quot;},{&quot;value&quot;:&quot;Z731&quot;,&quot;label&quot;:&quot;KIRIBATI&quot;},{&quot;value&quot;:&quot;Z160&quot;,&quot;label&quot;:&quot;KOSOVO&quot;},{&quot;value&quot;:&quot;Z227&quot;,&quot;label&quot;:&quot;KUWAIT&quot;},{&quot;value&quot;:&quot;Z256&quot;,&quot;label&quot;:&quot;KYRGYZSTAN&quot;},{&quot;value&quot;:&quot;Z228&quot;,&quot;label&quot;:&quot;LAO PEOPLE&apos;S DEMOCRATIC REPUBLIC&quot;},{&quot;value&quot;:&quot;Z359&quot;,&quot;label&quot;:&quot;LESOTHO&quot;},{&quot;value&quot;:&quot;Z145&quot;,&quot;label&quot;:&quot;LETTONIA&quot;},{&quot;value&quot;:&quot;Z229&quot;,&quot;label&quot;:&quot;LIBANO&quot;},{&quot;value&quot;:&quot;Z325&quot;,&quot;label&quot;:&quot;LIBERIA&quot;},{&quot;value&quot;:&quot;Z326&quot;,&quot;label&quot;:&quot;LIBIA&quot;},{&quot;value&quot;:&quot;Z119&quot;,&quot;label&quot;:&quot;LIECHTENSTEIN&quot;},{&quot;value&quot;:&quot;Z146&quot;,&quot;label&quot;:&quot;LITUANIA&quot;},{&quot;value&quot;:&quot;Z120&quot;,&quot;label&quot;:&quot;LUSSEMBURGO&quot;},{&quot;value&quot;:&quot;Z231&quot;,&quot;label&quot;:&quot;MACAU&quot;},{&quot;value&quot;:&quot;Z148&quot;,&quot;label&quot;:&quot;MACEDONIA&quot;},{&quot;value&quot;:&quot;Z327&quot;,&quot;label&quot;:&quot;MADAGASCAR&quot;},{&quot;value&quot;:&quot;Z328&quot;,&quot;label&quot;:&quot;MALAWI&quot;},{&quot;value&quot;:&quot;Z247&quot;,&quot;label&quot;:&quot;MALAYSIA&quot;},{&quot;value&quot;:&quot;Z232&quot;,&quot;label&quot;:&quot;MALDIVE&quot;},{&quot;value&quot;:&quot;Z329&quot;,&quot;label&quot;:&quot;MALI&quot;},{&quot;value&quot;:&quot;Z121&quot;,&quot;label&quot;:&quot;MALTA&quot;},{&quot;value&quot;:&quot;Z122&quot;,&quot;label&quot;:&quot;MAN&quot;},{&quot;value&quot;:&quot;Z710&quot;,&quot;label&quot;:&quot;MARIANNE DEL NORD&quot;},{&quot;value&quot;:&quot;Z330&quot;,&quot;label&quot;:&quot;MAROCCO&quot;},{&quot;value&quot;:&quot;Z513&quot;,&quot;label&quot;:&quot;MARTINICA&quot;},{&quot;value&quot;:&quot;Z331&quot;,&quot;label&quot;:&quot;MAURITANIA&quot;},{&quot;value&quot;:&quot;Z332&quot;,&quot;label&quot;:&quot;MAURITIUS&quot;},{&quot;value&quot;:&quot;Z360&quot;,&quot;label&quot;:&quot;MAYOTTE&quot;},{&quot;value&quot;:&quot;Z514&quot;,&quot;label&quot;:&quot;MESSICO&quot;},{&quot;value&quot;:&quot;Z735&quot;,&quot;label&quot;:&quot;MICRONESIA&quot;},{&quot;value&quot;:&quot;Z140&quot;,&quot;label&quot;:&quot;MOLDAVIA&quot;},{&quot;value&quot;:&quot;Z123&quot;,&quot;label&quot;:&quot;MONACO&quot;},{&quot;value&quot;:&quot;Z233&quot;,&quot;label&quot;:&quot;MONGOLIA&quot;},{&quot;value&quot;:&quot;Z159&quot;,&quot;label&quot;:&quot;MONTENEGRO&quot;},{&quot;value&quot;:&quot;Z531&quot;,&quot;label&quot;:&quot;MONTSERRAT&quot;},{&quot;value&quot;:&quot;Z333&quot;,&quot;label&quot;:&quot;MOZAMBICO&quot;},{&quot;value&quot;:&quot;Z206&quot;,&quot;label&quot;:&quot;MYANMAR&quot;},{&quot;value&quot;:&quot;Z300&quot;,&quot;label&quot;:&quot;NAMIBIA&quot;},{&quot;value&quot;:&quot;Z713&quot;,&quot;label&quot;:&quot;NAURU&quot;},{&quot;value&quot;:&quot;Z234&quot;,&quot;label&quot;:&quot;NEPAL&quot;},{&quot;value&quot;:&quot;Z515&quot;,&quot;label&quot;:&quot;NICARAGUA&quot;},{&quot;value&quot;:&quot;Z334&quot;,&quot;label&quot;:&quot;NIGER&quot;},{&quot;value&quot;:&quot;Z335&quot;,&quot;label&quot;:&quot;NIGERIA&quot;},{&quot;value&quot;:&quot;Z714&quot;,&quot;label&quot;:&quot;NIUE&quot;},{&quot;value&quot;:&quot;Z125&quot;,&quot;label&quot;:&quot;NORVEGIA&quot;},{&quot;value&quot;:&quot;Z719&quot;,&quot;label&quot;:&quot;NUOVA ZELANDA&quot;},{&quot;value&quot;:&quot;Z235&quot;,&quot;label&quot;:&quot;OMAN&quot;},{&quot;value&quot;:&quot;Z126&quot;,&quot;label&quot;:&quot;PAESI BASSI&quot;},{&quot;value&quot;:&quot;Z236&quot;,&quot;label&quot;:&quot;PAKISTAN&quot;},{&quot;value&quot;:&quot;Z734&quot;,&quot;label&quot;:&quot;PALAU REPUBBLICA&quot;},{&quot;value&quot;:&quot;Z516&quot;,&quot;label&quot;:&quot;PANAMA&quot;},{&quot;value&quot;:&quot;Z730&quot;,&quot;label&quot;:&quot;PAPUA NUOVA GUINEA&quot;},{&quot;value&quot;:&quot;Z610&quot;,&quot;label&quot;:&quot;PARAGUAY&quot;},{&quot;value&quot;:&quot;Z611&quot;,&quot;label&quot;:&quot;PERU&apos;&quot;},{&quot;value&quot;:&quot;Z722&quot;,&quot;label&quot;:&quot;PITCAIRN&quot;},{&quot;value&quot;:&quot;Z723&quot;,&quot;label&quot;:&quot;POLINESIA FRANCESE&quot;},{&quot;value&quot;:&quot;Z127&quot;,&quot;label&quot;:&quot;POLONIA&quot;},{&quot;value&quot;:&quot;Z128&quot;,&quot;label&quot;:&quot;PORTOGALLO&quot;},{&quot;value&quot;:&quot;Z518&quot;,&quot;label&quot;:&quot;PUERTO RICO&quot;},{&quot;value&quot;:&quot;Z237&quot;,&quot;label&quot;:&quot;QATAR&quot;},{&quot;value&quot;:&quot;Z114&quot;,&quot;label&quot;:&quot;REGNO UNITO&quot;},{&quot;value&quot;:&quot;Z156&quot;,&quot;label&quot;:&quot;REPUBBLICA CECA&quot;},{&quot;value&quot;:&quot;Z308&quot;,&quot;label&quot;:&quot;REPUBBLICA CENTRAFRICANA&quot;},{&quot;value&quot;:&quot;Z907&quot;,&quot;label&quot;:&quot;REPUBBLICA DEL SUD SUDAN&quot;},{&quot;value&quot;:&quot;Z505&quot;,&quot;label&quot;:&quot;REPUBBLICA DOMINICANA&quot;},{&quot;value&quot;:&quot;Z324&quot;,&quot;label&quot;:&quot;REUNION&quot;},{&quot;value&quot;:&quot;Z129&quot;,&quot;label&quot;:&quot;ROMANIA&quot;},{&quot;value&quot;:&quot;Z338&quot;,&quot;label&quot;:&quot;RUANDA&quot;},{&quot;value&quot;:&quot;Z154&quot;,&quot;label&quot;:&quot;RUSSIA&quot;},{&quot;value&quot;:&quot;Z339&quot;,&quot;label&quot;:&quot;SAHARA OCCIDENTALE&quot;},{&quot;value&quot;:&quot;Z533&quot;,&quot;label&quot;:&quot;SAINT CHRISTOPHER E NEVIS&quot;},{&quot;value&quot;:&quot;Z527&quot;,&quot;label&quot;:&quot;SAINT LUCIA&quot;},{&quot;value&quot;:&quot;Z528&quot;,&quot;label&quot;:&quot;SAINT VINCENT E GRANADINE&quot;},{&quot;value&quot;:&quot;Z726&quot;,&quot;label&quot;:&quot;SAMOA&quot;},{&quot;value&quot;:&quot;Z130&quot;,&quot;label&quot;:&quot;SAN MARINO&quot;},{&quot;value&quot;:&quot;Z340&quot;,&quot;label&quot;:&quot;SANT&apos;ELENA&quot;},{&quot;value&quot;:&quot;Z341&quot;,&quot;label&quot;:&quot;SAO TOME&apos; E PRINCIPE&quot;},{&quot;value&quot;:&quot;Z343&quot;,&quot;label&quot;:&quot;SENEGAL&quot;},{&quot;value&quot;:&quot;Z158&quot;,&quot;label&quot;:&quot;SERBIA&quot;},{&quot;value&quot;:&quot;Z342&quot;,&quot;label&quot;:&quot;SEYCHELLES&quot;},{&quot;value&quot;:&quot;Z344&quot;,&quot;label&quot;:&quot;SIERRA LEONE&quot;},{&quot;value&quot;:&quot;Z248&quot;,&quot;label&quot;:&quot;SINGAPORE&quot;},{&quot;value&quot;:&quot;Z240&quot;,&quot;label&quot;:&quot;SIRIA&quot;},{&quot;value&quot;:&quot;Z155&quot;,&quot;label&quot;:&quot;SLOVACCHIA&quot;},{&quot;value&quot;:&quot;Z150&quot;,&quot;label&quot;:&quot;SLOVENIA&quot;},{&quot;value&quot;:&quot;Z345&quot;,&quot;label&quot;:&quot;SOMALIA&quot;},{&quot;value&quot;:&quot;Z131&quot;,&quot;label&quot;:&quot;SPAGNA&quot;},{&quot;value&quot;:&quot;Z209&quot;,&quot;label&quot;:&quot;SRI LANKA&quot;},{&quot;value&quot;:&quot;Z403&quot;,&quot;label&quot;:&quot;ST. PIERRE AND MIQUELON&quot;},{&quot;value&quot;:&quot;Z404&quot;,&quot;label&quot;:&quot;STATI UNITI D&apos;AMERICA&quot;},{&quot;value&quot;:&quot;Z347&quot;,&quot;label&quot;:&quot;SUD AFRICA&quot;},{&quot;value&quot;:&quot;Z348&quot;,&quot;label&quot;:&quot;SUDAN&quot;},{&quot;value&quot;:&quot;Z207&quot;,&quot;label&quot;:&quot;SULTANATO DEL BRUNEI&quot;},{&quot;value&quot;:&quot;Z608&quot;,&quot;label&quot;:&quot;SURINAME&quot;},{&quot;value&quot;:&quot;Z132&quot;,&quot;label&quot;:&quot;SVEZIA&quot;},{&quot;value&quot;:&quot;Z133&quot;,&quot;label&quot;:&quot;SVIZZERA&quot;},{&quot;value&quot;:&quot;Z257&quot;,&quot;label&quot;:&quot;TAGIKISTAN&quot;},{&quot;value&quot;:&quot;Z217&quot;,&quot;label&quot;:&quot;TAIWAN&quot;},{&quot;value&quot;:&quot;Z357&quot;,&quot;label&quot;:&quot;TANZANIA&quot;},{&quot;value&quot;:&quot;Z161&quot;,&quot;label&quot;:&quot;TERRITORI PALESTINESI&quot;},{&quot;value&quot;:&quot;Z241&quot;,&quot;label&quot;:&quot;THAILANDIA&quot;},{&quot;value&quot;:&quot;Z242&quot;,&quot;label&quot;:&quot;TIMOR-LESTE&quot;},{&quot;value&quot;:&quot;Z351&quot;,&quot;label&quot;:&quot;TOGO&quot;},{&quot;value&quot;:&quot;Z727&quot;,&quot;label&quot;:&quot;TOKELAU&quot;},{&quot;value&quot;:&quot;Z728&quot;,&quot;label&quot;:&quot;TONGA&quot;},{&quot;value&quot;:&quot;Z612&quot;,&quot;label&quot;:&quot;TRINIDAD E TOBAGO&quot;},{&quot;value&quot;:&quot;Z352&quot;,&quot;label&quot;:&quot;TUNISIA&quot;},{&quot;value&quot;:&quot;Z243&quot;,&quot;label&quot;:&quot;TURCHIA&quot;},{&quot;value&quot;:&quot;Z258&quot;,&quot;label&quot;:&quot;TURKMENISTAN&quot;},{&quot;value&quot;:&quot;Z732&quot;,&quot;label&quot;:&quot;TUVALU&quot;},{&quot;value&quot;:&quot;Z138&quot;,&quot;label&quot;:&quot;UCRAINA&quot;},{&quot;value&quot;:&quot;Z353&quot;,&quot;label&quot;:&quot;UGANDA&quot;},{&quot;value&quot;:&quot;Z134&quot;,&quot;label&quot;:&quot;UNGHERIA&quot;},{&quot;value&quot;:&quot;Z613&quot;,&quot;label&quot;:&quot;URUGUAY&quot;},{&quot;value&quot;:&quot;Z259&quot;,&quot;label&quot;:&quot;UZBEKISTAN&quot;},{&quot;value&quot;:&quot;Z733&quot;,&quot;label&quot;:&quot;VANUATU&quot;},{&quot;value&quot;:&quot;Z614&quot;,&quot;label&quot;:&quot;VENEZUELA&quot;},{&quot;value&quot;:&quot;Z251&quot;,&quot;label&quot;:&quot;VIETNAM&quot;},{&quot;value&quot;:&quot;Z246&quot;,&quot;label&quot;:&quot;YEMEN&quot;},{&quot;value&quot;:&quot;Z355&quot;,&quot;label&quot;:&quot;ZAMBIA&quot;},{&quot;value&quot;:&quot;Z337&quot;,&quot;label&quot;:&quot;ZIMBABWE&quot;}]},&quot;GetStatoStatus&quot;:true,&quot;GetComune&quot;:[],&quot;GetComuneStatus&quot;:true,&quot;TryCatchBlock1&quot;:null,&quot;TryCatchBlock1Status&quot;:true,&quot;ipResponse&quot;:{&quot;error&quot;:&quot;OK&quot;,&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;result&quot;:&quot;{\n  \&quot;status\&quot;: 400,\n  \&quot;error\&quot;: {\n    \&quot;errorDescription\&quot;: \&quot;Invalid value &apos;-&apos; for query parameter idDocumento. expected type: Number, found: String\&quot;,\n    \&quot;errorStatus\&quot;: \&quot;APIKIT:BAD_REQUEST\&quot;,\n    \&quot;errorMessage\&quot;: \&quot;Invalid value &apos;-&apos; for query parameter idDocumento. expected type: Number, found: String\&quot;,\n    \&quot;errorPayload\&quot;: {\n      \&quot;response\&quot;: \&quot;Unexpected Error\&quot;\n    }\n  },\n  \&quot;correlationId\&quot;: \&quot;6080f24f-621d-4674-aa5d-69bee3b6311f\&quot;,\n  \&quot;timestamp\&quot;: \&quot;2025-08-01T14:24:52.804764425Z\&quot;\n}&quot;,&quot;statusCode&quot;:400,&quot;status&quot;:&quot;Bad Request&quot;},&quot;IntegrationServiceStatus&quot;:true,&quot;SetParamsQueryStatus&quot;:true,&quot;params&quot;:{&quot;Params&quot;:{&quot;QueryParam&quot;:{&quot;idDocumento&quot;:&quot;-&quot;},&quot;PathParam&quot;:{&quot;ciu&quot;:&quot;38910&quot;,&quot;codiceCompagnia&quot;:&quot;unipolsai&quot;}}},&quot;SetParamsUrlStatus&quot;:true,&quot;GetCorrectDocId&quot;:{&quot;codiceDocumentaleAllegato&quot;:&quot;-&quot;,&quot;integerDocumentId&quot;:null,&quot;documentId&quot;:&quot;-&quot;,&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;error&quot;:&quot;OK&quot;},&quot;GetCorrectDocIdStatus&quot;:true,&quot;ExtractUserInfos&quot;:[{&quot;username&quot;:&quot;Maria Calvino&quot;,&quot;ciu&quot;:1719,&quot;userId&quot;:&quot;101853XF&quot;,&quot;codiceCanale&quot;:&quot;AGE&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;compagniaNumber&quot;:&quot;1&quot;,&quot;companyId&quot;:&quot;SOC_1&quot;,&quot;profileName&quot;:&quot;Unipol Responsabile User&quot;,&quot;codiceAgenzia&quot;:&quot;01853&quot;},{&quot;username&quot;:&quot;Maria Calvino&quot;,&quot;ciu&quot;:1719,&quot;userId&quot;:&quot;101853XF&quot;,&quot;codiceCanale&quot;:&quot;AGE&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;,&quot;compagniaNumber&quot;:&quot;1&quot;,&quot;companyId&quot;:&quot;SOC_1&quot;,&quot;profileName&quot;:&quot;Unipol Responsabile User&quot;,&quot;codiceAgenzia&quot;:&quot;01853&quot;}],&quot;ExtractUserInfosStatus&quot;:true,&quot;GenericLog&quot;:{&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;error&quot;:&quot;OK&quot;},&quot;GenericLogStatus&quot;:true,&quot;options&quot;:{},&quot;trigger&quot;:&quot;true&quot;,&quot;tipoDocumento&quot;:&quot;CDN&quot;,&quot;accountDetailsId&quot;:&quot;a1i9X000006E9mWQAS&quot;,&quot;activeUserId&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;idDocumento&quot;:&quot;9744988&quot;,&quot;ciu&quot;:&quot;38910&quot;,&quot;compagnia&quot;:&quot;unipolsai&quot;}</sampleDataSourceResponse>
    <versionNumber>12</versionNumber>
</OmniUiCard>
