public with sharing class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object checkAggiorna(Map<String, Object> ipInput) {
        Map<String, Object> outputMap = new Map<String,Object>();
        Map<String, Object> optionsMap = new Map<String,Object>();
        //Input Parameter: ciu
        FEA_Helper.checkAggiorna(ipInput, outputMap, optionsMap);
        return outputMap;
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object refreshStatoEmail(Map<String, Object> ipInput) {
        Map<String, Object> outputMap = new Map<String,Object>();
        Map<String, Object> optionsMap = new Map<String,Object>();
        //Input Parameter: ciu
        FEA_Helper.refreshStatoEmail(ipInput, outputMap, optionsMap);
        return outputMap;
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object getContatti(Map<String, Object> ipInput) {
        //AC: riciclo AddressService per invocare la procedura
        return AddressService.invokeIntegrationProcedureLookup('RestGet_FeaContatti', ipInput, null);
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object getStampe(Map<String, Object> ipInput) {
        //AC: riciclo AddressService per invocare la procedura
        if((String) ipInput.get('processType') == 'FEA'){
        return AddressService.invokeIntegrationProcedureLookup('RestGet_FeaLogStampa', ipInput, null);
        } else {
            return AddressService.invokeIntegrationProcedureLookup('RestGet_PrivacyLogStampa', ipInput, null);
        }
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object getFea(Map<String, Object> ipInput) {
        //AC: riciclo AddressService per invocare la procedura
        return AddressService.invokeIntegrationProcedureLookup('RestGet_FeaGetDati', ipInput, null);
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object getDocumentaleLogStampa(Map<String, Object> ipInput) {
        //@GP: riciclo AddressService per invocare la procedura che recupera i documenti codificati in Base64
        //Params: ciu, tipoDocAnagrafica, idDocAnagrafica 
        System.debug('@GP ipInput: ' + ipInput);
        return AddressService.invokeIntegrationProcedureLookup('RestGet_FeaDownloadDocumentale', ipInput, null);
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object getContattiForm(Map<String, String> data) {
        String recordId = data.get('id');
        User activeUser = [SELECT Id, Name, IdAzienda__c, FederationIdentifier FROM User WHERE Id =: userInfo.getUserId() LIMIT 1];    
    
        //Controllare NPI 
        List<AccountDetails__c> accDetList = [SELECT /*FeaMail__c, FeaMobile__c, Email__c, Mobile__c, OtherEmail__c, OtherMobile__c,*/ RecordType.DeveloperName,
            AccountDetailsNPI__r.Email__c, AccountDetailsNPI__r.Mobile__c, AccountDetailsNPI__r.OtherEmail__c, AccountDetailsNPI__r.OtherMobile__c
            FROM AccountDetails__c WHERE Relation__r.FinServ__Account__c = :recordId and Recordtype.DeveloperName IN ('PA','Individual') ];
        //Non è presente NPI perchè già filtrati dalla visibilità User
        AccountAgencyDetails__c accountAgeDetail = [SELECT Id, Email__c, Mobile__c FROM AccountAgencyDetails__c WHERE Relation__r.FinServ__Account__c = :recordId 
            AND Relation__r.FinServ__RelatedAccount__c =: activeUser.IdAzienda__c];
            Map<String, String> response = new Map<String, String>();
        
        Boolean recordFea = false;
        Boolean recordPA = false;

        for (AccountDetails__c r : accDetList) {
            if (r.RecordType.DeveloperName.equalsIgnoreCase('Individual') && !String.isBlank(r.AccountDetailsNPI__r.Email__c) && !String.isBlank(r.AccountDetailsNPI__r.Mobile__c)) {
                System.debug('@GP contatti: ' + r.AccountDetailsNPI__r.Email__c + ' ' + r.AccountDetailsNPI__r.Mobile__c);
                recordFea = true;
                response.put('mobile', r.AccountDetailsNPI__r.Mobile__c); 
                response.put('email', r.AccountDetailsNPI__r.Email__c); 
                return response;
            }
        }
        if(!recordFea){
            for(AccountDetails__c r : accDetList){
                if (r.RecordType.DeveloperName.equalsIgnoreCase('PA') && !String.isBlank(r.AccountDetailsNPI__r.Mobile__c) && !String.isBlank(r.AccountDetailsNPI__r.Email__c)){
                    System.debug('@GP contatti: ' + r.AccountDetailsNPI__r.Email__c + ' ' + r.AccountDetailsNPI__r.Mobile__c);
                    recordPA = true;
                    response.put('mobile', r.AccountDetailsNPI__r.Mobile__c);
                    response.put('email', r.AccountDetailsNPI__r.Email__c); 
                    return response;
                }
            }
        }
        if(accountAgeDetail != null && !recordFea && !recordPA){
            response.put('otherMobile', accountAgeDetail.Mobile__c); //CAMPI CONFERMATI
            response.put('otherEmail', accountAgeDetail.Email__c); //CAMPI CONFERMATI
        }

        return response;
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object conferma(Map<String, Object> data) {
        //Controllo dati univoci
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();
        FEA_Helper.checkDatiUnivoci(data, outputMap, optionsMap);
        System.debug('@GP outputMap checkDatiUnivoci: ' + outputMap);
        //Costruzione xml dentro If
        if(outputMap.get('status') == true){
            outputMap.clear();
            FEA_Helper.createBarcodeAndXML(data, outputMap, optionsMap);
        }
        return outputMap;
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object revoca(Map<String, Object> data) {
        //@GP: riciclo AddressService per invocare la procedura che invia un aggiornamento o la revoca della FEA
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();
        FEA_Helper.revocaFEA(data, outputMap, optionsMap);

        if(outputMap.get('status') == true){
            //Params: contactTypeFilter, revoca, body 
            Map<String,Object> ipInput = new Map<String,Object>();
            ipInput.put('body',(String) outputMap.get('requestPayload'));
            ipInput.put('activeUserId',(String) outputMap.get('activeUserId'));
            ipInput.put('accountDetailsId',(String) outputMap.get('accountDetailsId'));
            ipInput.put('mostraDatiTracciatura',true);
            System.debug('@GP ipInput: ' + ipInput);
            return AddressService.invokeIntegrationProcedureLookup('RestGet_aggiornamentoFea', ipInput, null);
        } else {
            return outputMap;
        }
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object checkFeaAssociazioneStrumenti(Map<String, Object> data) {
        //Params: compagnia (1,2,3,4), codiceFiscale 
        return AddressService.invokeIntegrationProcedureLookup('RestGet_FeaAssociazioneStrumenti', data, null);
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object checkDocumento(Map<String, String> data) {
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();
        FEA_Helper.checkActiveDocument(data, outputMap, optionsMap);
        return outputMap;
    }

    /******************************************************************************************
    * @description 
    * @param        Map<String, Object> data
    * @return       Object
    *******************************************************************************************/
    @AuraEnabled(cacheable=false)
    public static Object certifcazioneContatti(Map<String, String> data) {
        Map<String,Object> outputMap = new Map<String,Object>();
        Map<String,Object> optionsMap = new Map<String,Object>();
        //Input Parameters: email, cellulare, ciu
        FEA_Helper.certifcazioneContatti(data, outputMap, optionsMap);

        //GP: richiamo la procedure
        Map<String,Object> ipInput = new Map<String,Object>();
        ipInput.put('body',(String)outputMap.get('requestPayload'));
        return AddressService.invokeIntegrationProcedureLookup('RestGet_certifcazioneContattiFEA', ipInput, null);
    }

}