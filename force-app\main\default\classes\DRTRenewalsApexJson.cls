global with sharing class DRTRenewalsApexJson implements System.Callable {

    // Entry point Callable
    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> inputMap  = (Map<String, Object>) args.get('input');
        Map<String, Object> outMap    = (Map<String, Object>) args.get('output');
        Map<String, Object> options   = (Map<String, Object>) args.get('options');
        return invokeMethod(action, inputMap, outMap, options);
    }

    // Dispatcher
    global Boolean invokeMethod(
        String methodName,
        Map<String, Object> inputMap,
        Map<String, Object> outMap,
        Map<String, Object> options
    ) {
        Boolean result = true;
        try {
            if (methodName == 'getSchemaInit') {
                getSchemaInit(inputMap, outMap);
            } else {
                outMap.put('success', false);
                outMap.put('message', 'Metodo non riconosciuto: ' + methodName);
                result = false;
            }
        } catch (Exception e) {
            outMap.put('success', false);
            outMap.put('message', 'Errore generico: ' + e.getMessage());
        }
        return result;
    }

    // Recupera tutti i dati per inizializzare la UI
    private void getSchemaInit(
        Map<String, Object> inputMap,
        Map<String, Object> outMap
    ) {
        try {
            // 1️⃣ Dati utente
            Id currentUserId = UserInfo.getUserId();
            User currentUser = [
                SELECT Profile.Name, FiscalCode__c
                FROM User
                WHERE Id = :currentUserId
                LIMIT 1
            ];
            String profileName = currentUser.Profile.Name;
            String fiscalCode  = currentUser.FiscalCode__c;
            String referentValue = fiscalCode;
            // 2️⃣ Referent list (solo per responsabile user)
            List<Map<String, String>> referentList = new List<Map<String, String>>();
            if (profileName == 'Unipol Responsabile User') {
                Set<Id> agencyIds = getAgencyIdsForUser(fiscalCode);
                if (!agencyIds.isEmpty()) {
                    // prendo tutti i fiscalCode dei NetworkUser per queste agenzie
                    Set<String> fiscalCodes = new Set<String>();
                    for (NetworkUser__c nu : [
                        SELECT FiscalCode__c
                        FROM NetworkUser__c
                        WHERE Agency__c IN :agencyIds
                          AND FiscalCode__c != null
                    ]) {
                        fiscalCodes.add(nu.FiscalCode__c);
                    }
                    referentValue = String.join(new List<String>(fiscalCodes), ';');
                    // recupero gli User Salesforce corrispondenti
                    if (!fiscalCodes.isEmpty()) {
                        for (User u : [
                            SELECT Name, FederationIdentifier
                            FROM User
                            WHERE FederationIdentifier IN :fiscalCodes
                        ]) {
                            referentList.add(new Map<String, String>{
                                'label' => u.Name,
                                'value' => u.FederationIdentifier
                            });
                        }
                    }
                }
            }

            // 3️⃣ Agency list (per DRTGetReferenceList)
            Set<Id> agencyIdsForSchema = getAgencyIdsForUser(fiscalCode);
            List<FinServ__AccountAccountRelation__c> relations = agencyIdsForSchema.isEmpty()
                ? new List<FinServ__AccountAccountRelation__c>()
                : buildAccountRelationList(agencyIdsForSchema);
            Set<String> agencyList = buildReferenceList(relations);

            // 4️⃣ Date & strutture statiche
            Date today = Date.today();
            List<Map<String, Object>> periods = new List<Map<String, Object>>{
                new Map<String, Object>{'label'=>'Oggi','value'=>'today'},
                new Map<String, Object>{'label'=>'Settimana corrente','value'=>'week'},
                new Map<String, Object>{'label'=>'Mese corrente','value'=>'month'},
                new Map<String, Object>{'label'=>'Da - A','value'=>'fromTo'}
            };
            List<Map<String, Object>> sections = new List<Map<String, Object>>{
                new Map<String, Object>{'label'=>'Tutti','value'=>'RCA;REL;VIT'},
                new Map<String, Object>{'label'=>'Auto','value'=>'RCA'},
                new Map<String, Object>{'label'=>'Danni Non Auto','value'=>'REL'},
                new Map<String, Object>{'label'=>'Vita','value'=>'VIT'}
            };
            List<Map<String, Object>> rates = new List<Map<String, Object>>{
                new Map<String, Object>{'label'=>'Si','value'=>true},
                new Map<String, Object>{'label'=>'No','value'=>false}
            };

            // 5️⃣ Costruisco il risultato
            Map<String, Object> schemaResult = new Map<String, Object>{
                'periods'      => periods,
                'rate'         => false,
                'fromDate'     => formatDates(today.toStartofWeek()),
                'minDateStart' => formatDates(today.addMonths(-6)),
                'sections'     => sections,
                'defaultDateTo'=> formatDates(today),
                'todayDate'    => formatDates(today),
                'section'      => 'RCA;REL;VIT',
                'toDate'       => formatDates(today),
                'rates'        => rates,
                'referent'     => referentValue,
                'referentList' => referentList,
                'period'       => 'week',
                'profileName'  => profileName,
                'agencyList'   => agencyList,
                'compagnia' => '1;4'
            };

            outMap.put('result',  schemaResult);
            outMap.put('success', true);
        } catch (Exception e) {
            outMap.put('success', false);
            outMap.put('message', 'Errore in getSchemaInit: ' + e.getMessage());
        }
    }

    // Helper: recupera gli Id delle agenzie per un dato codice fiscale
    private Set<Id> getAgencyIdsForUser(String fiscalCode) {
        Set<Id> agencyIds = new Set<Id>();
        for (NetworkUser__c nu : [
            SELECT Agency__c
            FROM NetworkUser__c
            WHERE FiscalCode__c = :fiscalCode
              AND Agency__c != null
        ]) {
            agencyIds.add(nu.Agency__c);
        }
        return agencyIds;
    }

    // Helper: recupera le relazioni account–agency
    private List<FinServ__AccountAccountRelation__c> buildAccountRelationList(Set<Id> agencyIds) {
        return [
            SELECT RecordType.DeveloperName,
                   FinServ__Account__c,
                   Identifier__c,
                   FinServ__ExternalId__c
            FROM FinServ__AccountAccountRelation__c
            WHERE FinServ__Account__c IN :agencyIds
              AND RecordType.DeveloperName = 'AgencySociety'
        ];
    }

    // Helper: costruisce il Set<String> agencyList a partire dalle relazioni
    private Set<String> buildReferenceList(List<FinServ__AccountAccountRelation__c> relations) {
        Set<String> ids = new Set<String>();
        for (FinServ__AccountAccountRelation__c rel : relations) {
            if (rel.Identifier__c != null && rel.FinServ__ExternalId__c != null) {
                Integer idx = rel.FinServ__ExternalId__c.lastIndexOf('SOC_');
                if (idx != -1 && rel.FinServ__ExternalId__c.length() > idx + 4) {
                    String suffix = rel.FinServ__ExternalId__c.substring(idx + 4);
                    ids.add(suffix + rel.Identifier__c);
                }
            }
        }
        return ids;
    }

    private static String formatDates(Date t) {
        Integer d = t.day();
        Integer m = t.month();
        Integer y = t.year();
        String dd = (d < 10 ? '0' + d : '' + d);
        String mm = (m < 10 ? '0' + m : '' + m);
        return dd + '-' + mm + '-' + y;
    }

}