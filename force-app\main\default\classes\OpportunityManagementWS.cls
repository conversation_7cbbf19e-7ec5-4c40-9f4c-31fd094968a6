/**
 * @File Name         : OpportunityManagementWS.cls
 * @Description       : 
 * <AUTHOR> <EMAIL>
 * @Group             : 
 * @Last Modified On  : 04-30-2025
 * @Last Modified By  : <EMAIL>
@cicd_tests OpportunityManagementWSTest
**/
@RestResource(urlMapping = '/v1/manageOpportunity')
global without sharing class OpportunityManagementWS
{
    @HttpPost
    global static void manageOpportunity()
    {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;

        Savepoint sp;

        try
        {
            RequestWrapper input = (RequestWrapper)JSON.deserialize(req.requestBody.toString(), RequestWrapper.class);
            System.debug('flowInput: ' + input);
           // Boolean isPerson = input.data.contractor.type == 'Individual';
   
           // Account customer = getCustomer(input, isPerson, res);
            Account customer = getCustomer(input, res);
            if (customer == null)
            {
                return;
            }

            Account agency = getAgency(input, res);
            if (agency == null)
            {
                return;
            }

            OpportunityConfiguration__mdt oppConfig = getOpportunityConfig(input, res);
            if (oppConfig == null)
            {
                return;
            }
            system.debug('%%Francesca R. oppConfig: '+oppConfig);
            ProductConfiguration__mdt prodConfig;
            if(input.data.channelCode != 'APP' && input.data.eventSource != null){ //Francesca R.
                prodConfig = getProductConfig(input, res);
                system.debug('%%Francesca R. prodConfig: '+prodConfig);
                if (prodConfig == null)
               {
                   return;
               }
            }
           
            OpportunityWSWrapper flowInput = input.data.payload;
            RequestActivity flowInputActivity = input.data.activity;
            if (flowInput != null)
            {
                flowInput.selectedByLocator = input.data.salesNetwork.selectedByLocator;
            }  
            //flowInput.cip = input.data.salesNetwork.cip;
            //System.debug('flowInput: ' +JSON.serialize(flowInput));
            system.debug('%%Francesca R. customerId: '+customer.Id);
            system.debug('%%Francesca R. agencyId: '+agency.Id);
            system.debug('%%Francesca R. flowInput: '+flowInput);

            system.debug('@GL objectType: '+ input.data.objectType);
            String contractId = input.data.contract.id;
            if (contractId == null)
            {system.debug('%%MG contract id vuoto');}
            else
            {system.debug('%%MG contractId: '+contractId);}
            Map<String, Object> inputVariables = new Map<String, Object>();
            inputVariables.put('eventType',input.data.eventType);
            inputVariables.put('customerId', customer.Id);
            inputVariables.put('customer', customer);
            inputVariables.put('agencyId', agency.Id);
            inputVariables.put('agency', agency);
            inputVariables.put('input', flowInput);
            inputVariables.put('contractId', contractId);  //Folder Id
            //Gallo da decommentare per call me back
            inputVariables.put('inputActivity', flowInputActivity);
            inputVariables.put('objectType', input.data.objectType);
            inputVariables.put('oppConfig', oppConfig);
            if(input.data.channelCode != 'APP' && input.data.eventSource != null &&input.data.eventSource != 'Pega'){ //Francesca R.
                inputVariables.put('prodConfig', prodConfig);
            }
            if(String.isNotBlank(input.data.contract.intermediaryId)){
                List<User> userList = [SELECT Id, FiscalCode__c FROM User WHERE FiscalCode__c = :input.data.contract.intermediaryId];
                if(!userList.isEmpty()){
                    inputVariables.put('intermediaryId', userList[0].Id);
                }
            }


        
            sp = Database.setSavepoint();
            Flow.Interview flowExecution = Flow.Interview.createInterview(oppConfig.MatchMergeFlowId__c, inputVariables);

            System.debug('@MM inputFlow: ' + JSON.serialize(inputVariables));
            flowExecution.start();
        
            String flowResponse = (String)flowExecution.getVariableValue('flowResponse');
            String flowError = (String)flowExecution.getVariableValue('flowError');
            
            system.debug('%%Francesca R. flowError: '+flowError);
            system.debug('%%Francesca R. flowResponse: '+flowResponse);
        
            if (flowError != null) {
                Database.rollback(sp);
                FlowException ex = new FlowException();
                ex.setMessage(oppConfig?.MatchMergeFlowId__c + ' : ' + flowError);
                throw ex;
            }
        
            setResponse(res, 200, flowResponse);
        }
        catch(Exception ex)
        {
            setResponse(res, 400, ex.getMessage());
            logError(ex, req, res);
        }
    }

    private static Account getCustomer(RequestWrapper input, RestResponse res) {
        List<Account> customerList = new List<Account>();
    
        if (Schema.sObjectType.Account.isAccessible()) {
          
                customerList = [SELECT Id, Name, IsPersonAccount,FirstName, LastName, Email__c, Phone, PersonBirthdate FROM Account WHERE ExternalId__c =: input.data.contractor.id LIMIT 1];
  
        } else {
            setResponse(res, 403, 'Insufficient permissions to access Account object');
            return null;
        }
    
        if (customerList.isEmpty()) {
            setResponse(res, 404, 'Customer not found');
            return null;
        }
        else
        {
            enrichCustomer(input, customerList.get(0), customerList.get(0).IsPersonAccount);
            System.debug('Customer found: ' + customerList.get(0).Id);
        }
    
        return customerList.get(0);
    }

    private static void enrichCustomer(RequestWrapper input, Account customer, Boolean isPerson)
    {
        if(Schema.SObjectType.Account.isUpdateable())
        {
            if(isPerson)
            {
                customer.FirstName = String.isBlank(customer.FirstName) ? input.data.contractor.firstName : customer.FirstName;
                customer.LastName = String.isBlank(customer.LastName) ? input.data.contractor.lastName : customer.LastName;
                customer.PersonBirthdate = customer.PersonBirthdate == null ? input.data.contractor.birthDate : customer.PersonBirthdate;
            }
            else
            {
                customer.Name = String.isBlank(customer.Name) ? input.data.contractor.businessName : customer.Name;
            }
            customer.Email__c = String.isBlank(customer.Email__c) ? input.data.contractor.email : customer.Email__c;
            customer.Phone = String.isBlank(customer.Phone) ? input.data.contractor.phoneNumber : customer.Phone;

            try 
            {
                update customer;
            }
            catch (Exception e)
            {
                System.debug(e);
            }
        }
    }

    private static Account getAgency(RequestWrapper input, RestResponse res) {
        String targetAgency = 'AGE_' + input.data.salesNetwork.agency;
        List<Account> agencyList = [SELECT Id,Name FROM Account WHERE ExternalId__c =: targetAgency AND IsRolledOut__c = true AND RecordType.DeveloperName = 'Agency' LIMIT 1];
    
        if (agencyList.isEmpty()) {
            setResponse(res, 404, 'Agency not found');
            return null;
        }
    
        return agencyList.get(0);
    }

    private static OpportunityConfiguration__mdt getOpportunityConfig(RequestWrapper input, RestResponse res) {
        if (Schema.sObjectType.OpportunityConfiguration__mdt.isAccessible()) {
            return [SELECT Id, DeveloperName, AutomaticMatching__c, AutomaticOpening__c, ManualOpening__c, AutomaticClosing__c, ManualClosing__c, AllowedCompanies__c, FlexCardId__c, MatchMergeFlowId__c, Merge__c, MatchableProducts__c, Split__c, ClosingType__c FROM OpportunityConfiguration__mdt WHERE DeveloperName =: input.data.channelCode LIMIT 1];
        } else {
            setResponse(res, 403, 'Insufficient permissions to access OpportunityConfiguration__mdt object');
            return null;
        }
    }
    
    private static ProductConfiguration__mdt getProductConfig(RequestWrapper input, RestResponse res) {
        if (Schema.sObjectType.ProductConfiguration__mdt.isAccessible()) {
            return [SELECT Id, DeveloperName, DomainType__c, QuoteComputation__c, ProductCode__c, Quote__c, Template__c FROM ProductConfiguration__mdt WHERE DomainType__c =: input.data.eventSource LIMIT 1];
        } else {
            setResponse(res, 403, 'Insufficient permissions to access ProductConfiguration__mdt object');
            return null;
        }
    }

    private static void setResponse(RestResponse res, Integer statusCode, String message)
    {
        Map<String, Object> responseBody = new Map<String, Object>();
        res.statusCode = statusCode;
        res.addHeader('Content-Type', 'application/json');
        responseBody.put('success', statusCode < 300);
        if(statusCode < 300)
        {
            responseBody.put('correlationId', (Map<String, Object>)JSON.deserializeUntyped(message));
            responseBody.put('tmstRegistration', System.now().format('yyyy-MM-dd HH:mm:ss', 'Europe/Rome'));
        }
        else
        {
            responseBody.put('message', message);
        }
        res.responseBody = Blob.valueOf(JSON.serialize(responseBody));
    }

    private static void logError(Exception exc, RestRequest request, RestResponse response)
    {
        HttpRequest convertedRequest = convertRequest(request);
        HttpResponse convertedResponse = convertResponse(response);
        ErrLogMngClass logger = new ErrLogMngClass();
        logger.integrationErrLog(exc, convertedRequest, convertedResponse, OpportunityManagementWS.class.getName());
    }

    private static HttpRequest convertRequest(RestRequest request)
    {
        HttpRequest result = new HttpRequest();
        result.setEndpoint(request.requestURI);
        result.setMethod(request.httpMethod);
        result.setBody(request.requestBody.toString());

        return result;
    }

    private static HttpResponse convertResponse(RestResponse response)
    {
        HttpResponse result = new HttpResponse();
        result.setBody(response.responseBody != null ? response.responseBody.toString() : null);
        result.setStatusCode(response.statusCode);

        return result;
    }

    private class RequestWrapper
    {
        public String id;
        public String source;
        public String type;
        public Datetime tmstRegistration;
        public RequestDataWrapper data;
    }

    private class RequestDataWrapper
    {
        public String objectId;
        public String objectType;
        public String eventId;
        public String eventType;
        public String type;
        public String eventOperation;
        public String eventSource;
        public Datetime eventIssueDate;
        public String companyCode;
        public String channelCode;
        public RequestSalesNetwork salesNetwork;
        public RequestBankInsurance bankInsurance;
        public RequestContract contract;
        public RequestContractor contractor;
        public RequestActivity activity;
        public OpportunityWSWrapper payload;
    }

    private class RequestSalesNetwork
    {
        public String agency;
        public String cip;
        public Boolean selectedByLocator;
    }

    private class RequestBankInsurance
    {
        public String bankId;
        public String branchId;
        public String agentId;
    }

    private class RequestContract
    {
        public String id;
        public String type;
        public String status;
        public String contractNumber;
        public String issuePortfolio;
        public String productCode;
        public Date validityStartDate;
        public Date validityEndDate;
        public Date effectiveStartDate;
        public Date effectiveEndDate;
        public Boolean flagOmniChannelSharing;
        public String intermediaryId;
    }

    private class RequestContractor
    {
        public String id;
        public String type;
        public String firstName;
        public String lastName;
        public String fiscalCode;
        public String vatNumber;
        public String businessName;
        public String privacyCode;
        public Date birthDate;
        public String email;
        public String phoneNumber;
    }


}