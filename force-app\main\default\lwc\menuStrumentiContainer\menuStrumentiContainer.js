import { LightningElement, track } from 'lwc';
import { getFocusedTabInfo, setTabLabel, setTabIcon } from 'lightning/platformWorkspaceApi';
import { ShowToastEvent } from "lightning/platformShowToastEvent";

import getTabs from '@salesforce/apex/MenuStrumentiController.getTabs';
import getSections from '@salesforce/apex/MenuStrumentiController.getSections';
import getTreeByContext from '@salesforce/apex/MenuStrumentiController.getTreeByContext';
import getUserFavorite from '@salesforce/apex/MenuStrumentiController.getUserFavorite';
import setUserFavorite from '@salesforce/apex/MenuStrumentiController.setUserFavorite';
import removeUserFavorite from '@salesforce/apex/MenuStrumentiController.removeUserFavorite';
import getParamsForFei from '@salesforce/apex/MenuStrumentiController.getParamsForFei';
import getUserCf from '@salesforce/apex/MenuStrumentiController.getUserCf';
import getUserContext from '@salesforce/apex/UserUtils.getUserContext';

import MENU_STRUMENTI_UNIPOL_LOGO from "@salesforce/resourceUrl/menuStrumentiUnipol";
import MENU_STRUMENTI_UNISALUTE_LOGO from "@salesforce/resourceUrl/menuStrumentiUnisalute";

//import { registerRefreshContainer, REFRESH_COMPLETE, REFRESH_COMPLETE_WITH_ERRORS, REFRESH_ERROR } from 'lightning/refresh';

// Implementare visibilità tramite mandato dei tab

export default class MenuStrumentiContainer extends LightningElement {
    
    @track _tabs = [];
    @track _favorite = [];

    unipolLogo = MENU_STRUMENTI_UNIPOL_LOGO;
    unisaluteLogo = MENU_STRUMENTI_UNISALUTE_LOGO;

    currentTab;
    notEnabled = false;

    isLoading = 0;

    paramsFEI = {}
    isLWCModalOpened;

    @track userContext = {};

    get userNetwork(){
        return Object.values(this.userContext?.userNetworks || {})?.find(network => {
          return network?.society?.toLowerCase() === this.currentTab?.toLowerCase(); 
        }) || {};
    }
    
    showLoader() {
        this.isLoading = true;
    }

    hideLoader() {
        this.isLoading = false;
    }

    connectedCallback() {
        console.log('MenuStrumentiContainer:: connected callback');

        this.init();
        //this.refreshContainerID = registerRefreshContainer(this, this.refreshContainer);
    }

    disconnectedCallback() {
        console.log("Component disconnected.");
    }

    /* eslint-disable no-console */
    async init() {
        this.showLoader();
        this.setTab();

        // inizializza gli array reattivi
        this._tabs = [];
        this._favorite = [];

        try {
            /* ------------------------------------------------------------------ *
            * 1. Recupero preferiti utente                                        *
            * ------------------------------------------------------------------ */
            this._favorite = await getUserFavorite();
            console.log(
                `MenuStrumentiContainer::getUserFavorite:: ${JSON.stringify(this._favorite)}`
            );

            /* ------------------------------------------------------------------ *
            * 2. Recupero contesto utente                                         *
            * ------------------------------------------------------------------ */
            this.userContext = await getUserContext();
            console.log(`MenuStrumentiContainer::getUserContext:: ${JSON.stringify(this.userContext)}`);

            /* ------------------------------------------------------------------ *
            * 3. Recupero tabs                                                    *
            * ------------------------------------------------------------------ */
            const tabsResult = await getTabs();
            console.log(`MenuStrumentiContainer::getTabs:: ${JSON.stringify(tabsResult)}`);

            if (!tabsResult || tabsResult.length === 0) {
                this.notEnabled = true;
                return;
            }

            // user context in base al

            // Popola lo scheletro dei tab
            this._tabs = tabsResult.map((el, idx) => ({
                name: el.name,
                label: el.label,
                context: el.context,
                userContext: el.userContext,
                isUnipol: el.name.includes('UNIPOL'),
                isUnisalute: el.name.includes('UNISALUTE'),
                isUnirental: el.name.includes('RENTAL'),
                isUnitech: el.name.includes('TECH'),
                show: idx === 0,
                tabClass:
                    idx === 0
                        ? 'slds-tabs_default__item slds-is-active'
                        : 'slds-tabs_default__item',
                tabSection:
                    idx === 0
                        ? 'slds-tabs_default__content slds-show'
                        : 'slds-tabs_default__content slds-hide',
                id: `tab-default-${el.name}__item`,
                ariaControls: `tab-default-${el.name}`,
                sections: []
            }));

            // seleziona il primo tab come corrente
            this.currentTab = this._tabs[0].name;

            /* ------------------------------------------------------------------ *
            * 4. Per ogni tab: sections + alberatura                              *
            *    Le due query sono indipendenti ⇒ Promise.all.                    *
            * ------------------------------------------------------------------ */
            await Promise.all(
                this._tabs.map(async tab => {
                    try {
                        // chiamate in parallelo
                        const [sectionList, treeContext] = await Promise.all([
                            getSections({ context: tab.context }),
                            getTreeByContext({
                                context: tab.context,
                                userContext: tab.userContext
                            })
                        ]);

                        /* --------------------------- Sections --------------------------- */
                        console.log(`MenuStrumentiContainer::getSections:: ${JSON.stringify(sectionList)}`);
                        sectionList.forEach(s => {
                            tab.sections.push({
                                sectionLabel: s.label,
                                sectionName: s.name,
                                sectionContext: s.context,
                                sectionShow: false,
                                sectionMenus: []
                            });
                        });

                        /* ----------------------- Alberatura / Menù ---------------------- */
                        console.log(`MenuStrumentiContainer::getTreeByContext:: ${JSON.stringify(treeContext)}`);
                        treeContext.forEach(wrapper => {
                            const target = tab.sections.find(
                                sec => sec.sectionName === wrapper.section
                            );
                            if (!target) return;

                            target.sectionMenus.push(...wrapper.items);
                            if (target.sectionMenus.length) target.sectionShow = true;
                        });

                        /* -------------- Flag preferiti (se presenti) -------------------- */
                        if (this._favorite?.length) {
                            tab.sections = tab.sections.map(sec => ({
                                ...sec,
                                sectionMenus: this.markFavorites(sec.sectionMenus)
                            }));
                        }
                    } catch (errTab) {
                        console.error(
                            `MenuStrumentiContainer::tab[${tab.context}]:: ${JSON.stringify(
                                errTab
                            )}`
                        );
                    }
                })
            );

            // forza reattività LWC
            this._tabs = [...this._tabs];
        } catch (err) {
            console.error(`MenuStrumentiContainer::init:: ${JSON.stringify(err)}`);
        } finally {
            this.hideLoader();
            console.log('MenuStrumentiContainer::init finished');
        }
    }

    async setTab() {
        const { tabId } = await getFocusedTabInfo();
        await setTabLabel(tabId, 'Strumenti', 'custom:custom19');
        // opzionale:
        await setTabIcon(tabId,'custom:custom19');
    }

    /*refreshContainer(refreshPromise) {
        console.log("Refreshing component...");
        // Esegui l'aggiornamento dei dati
        this.init(); // Metodo personalizzato per aggiornare i dati
        
        // Gestisci lo stato del refresh
        return refreshPromise.then((status) => {
            if (status === REFRESH_COMPLETE) {
                console.log("Refresh completed successfully!");
            
            } else if (status === REFRESH_COMPLETE_WITH_ERRORS) {
                console.warn("Refresh completed with some issues.");
            
            } else if (status === REFRESH_ERROR) {
                console.error("Refresh failed with major errors.");
            
            }
        });
        
    }*/

    handleTabClick(event) {
        event.preventDefault();

        this.currentTab = event.currentTarget.dataset.id;

        const currentItem = this.template.querySelector(
            'li.slds-tabs_default__item.slds-is-active'
        );
        if (currentItem) {
            currentItem.classList.remove('slds-is-active');

            const currentPanelId = currentItem.querySelector('a').getAttribute('aria-controls');
            const currentPanel = this.template.querySelector(
                `#${currentPanelId}`
            );
            
            if (currentPanel) {
                currentPanel.classList.replace('slds-show', 'slds-hide');
            }
        }

        // Aggiunge lo stato attivo al tab cliccato
        const clickedItem = event.currentTarget.parentElement; // <li>
        clickedItem.classList.add('slds-is-active');

        const panelId = event.currentTarget.getAttribute('aria-controls');
        const panel = this.template.querySelector(`#${panelId}`);
        if (panel) {
            panel.classList.replace('slds-hide', 'slds-show');
        }
    }

    handleBranchToggle(event) {
        const sectionName = event.detail.sectionName;
        const tabs = event.detail.tab;

        // per ogni sezione diversa da quella cliccata,
        // azzero tutti gli expanded dei suoi menu

        const target = this._tabs.find(tab => tab.name === tabs);

        console.log('MenuStrumentiContainer::handleBranchToggle:: ' + JSON.stringify(target.sections));

        target.sections = target.sections.map(sec => {
            if (sec.sectionName !== sectionName) {
            const collapsed = sec.sectionMenus.map(item => ({
                ...item,
                expanded: false
            }));
            return { ...sec, sectionMenus: collapsed };
            }
            return sec;
        });

        this._tabs = [...this._tabs];
    }

    /* eslint-disable no-console */
    async handleFavoriteSelected(event) {
        this.showLoader();

        const { node } = event.detail;
        console.log(
            `MenuStrumentiContainer::handleFavoriteSelected:: ${JSON.stringify(node)}`
        );

        /* ------------------------------------------------------------------ *
        * 1. Limite preferiti                                                 *
        * ------------------------------------------------------------------ */
        if ((this._favorite?.length ?? 0) >= 3) {
            this.sendAlert(
                'error',
                'Hai raggiunto il numero massimo di 3 preferiti. Per rimuovere i preferiti attuali cliccare nuovamente sulla stella'
            );
            this.hideLoader();
            return;
        }

        const currentTab = this._tabs.find(tab => tab.name === this.currentTab);
        if (!currentTab) {
            console.error('MenuStrumentiContainer::handleFavoriteSelected:: tab not found');
            this.hideLoader();
            return;
        }

        /* ------------------------------------------------------------------ *
        * 2. Persistenza nuovo preferito                                      *
        * ------------------------------------------------------------------ */
        try {
            await setUserFavorite({
                favLabel:      node.label,
                favDevName:    node.developerName,
                favType:       node.type,
                feiid:         node.feiId        ?? null,
                favParams:     node.params       ?? null,
                favLink:       node.redirectLink ?? null,
                requestType:   node.requestType  ?? null,
                userContext:   currentTab.userContext
            });

            console.log('MenuStrumentiContainer::setUserFavorite completed');

            // aggiorna cache locale
            this._favorite = [...(this._favorite ?? []), node.developerName];

            this.handleFavorite(event);        // refresh UI
            this.sendAlert('success', 'Elemento aggiunto ai preferiti');
        } catch (err) {
            console.error(
                `MenuStrumentiContainer::setUserFavorite:: ${JSON.stringify(err)}`
            );
            this.sendAlert('error', 'Errore nel salvataggio del preferito');
        } finally {
            this.hideLoader();
        }
    }


    /* eslint-disable no-console */
    async handleUnfavoriteSelected(event) {
        this.showLoader();

        const { developerName } = event.detail;
        console.log(
            `MenuStrumentiContainer::handleUnfavoriteSelected:: ${developerName}`
        );

        /* ------------------------------------------------------------------ *
        * 1. Verifica presenza tra i preferiti                                *
        * ------------------------------------------------------------------ */
        if (!this._favorite?.includes(developerName)) {
            this.hideLoader();
            return;
        }

        /* ottimistic update per UI reattiva */
        const snapshotFavorite = [...this._favorite];
        this._favorite = this._favorite.filter(name => name !== developerName);

        /* ------------------------------------------------------------------ *
        * 2. Rimozione lato server                                            *
        * ------------------------------------------------------------------ */
        try {
            await removeUserFavorite({ favorite: developerName });
            console.log('MenuStrumentiContainer::removeUserFavorite completed');

            this.handleFavorite(event);      // refresh UI (icona stella, ecc.)
            this.sendAlert('success', 'Elemento rimosso dai preferiti');
        } catch (err) {
            console.error(
                `MenuStrumentiContainer::removeUserFavorite:: ${JSON.stringify(err)}`
            );

            /* rollback locale in caso di errore */
            this._favorite = snapshotFavorite;
            this.sendAlert('error', 'Errore nella rimozione del preferito');
        } finally {
            this.hideLoader();
        }
    }


    handleFavorite(event) {
        const developerName = event.detail.developerName;

        // trova la tab attiva
        const target = this._tabs.find(tab => tab.name === this.currentTab);

        // aggiorna solo il nodo corrispondente dentro tutta la struttura
        target.sections = target.sections.map(sec => ({
            ...sec,
            sectionMenus: this.markFavorites(sec.sectionMenus, developerName)
        }));

        this._tabs = [...this._tabs];
    }

    markFavorites(nodes, developerName) {
        return nodes.map(node => {
            // se è il nodo cliccato = togglo il suo isFavorite
            // altrimenti lo lascio com'è
            let fav = false;
            if(developerName) {
                fav = node.developerName === developerName
                                ? !node.isFavorite
                                : node.isFavorite;
            } else {
                fav = this._favorite.includes(node.developerName);
            }

            return {
                ...node,
                isFavorite: fav,
                children: Array.isArray(node.children) && node.children.length
                    ? this.markFavorites(node.children, developerName)
                    : []
            };
        });
    }

    toggleLWCModal() {
        this.isLWCModalOpened = !this.isLWCModalOpened;
    }

    /* eslint-disable no-console */
    async handleNavigateFei(event) {
        this.showLoader();

        try {
            const { node } = event.detail;
            if (!node) throw new Error('node undefined in event.detail');

            this.paramsFEI = {};                               // reset
            const currentTab = this._tabs.find(t => t.name === this.currentTab);
            const userContext = currentTab?.userContext ?? null;

            /* ------------------------------------------------------------------ *
            * 1. FEI classico (chiamata a piattaforma FEI)                       *
            * ------------------------------------------------------------------ */
            if (node.type === 'FEI' && node.feiId) {
                console.log('Effettuo FEI normale');

                const fei = await getParamsForFei({
                    feiId: node.feiId,
                    userContext
                });

                /* payload → preferisci node.params, fallback a fei.params         */
                const rawPayload = node.params ?? fei.params ?? null;
                const feiRequestPayload = rawPayload !== null ? rawPayload : null;

                this.paramsFEI = {
                    feiId: node.feiId,
                    fiscalCode: fei.fiscalCode,
                    feiRequestPayload,
                    feiRequestType: node.requestType ?? null,
                    permissionSetName: fei.permissionSetName
                };
            }
            /* ------------------------------------------------------------------ *
            * 2. FEI link a servizi (GET / POST)                                 *
            * ------------------------------------------------------------------ */
            else {
                const cf = await getUserCf();
                console.log('Effettuo FEI link a servizi');

                if (node.requestType === 'GET') {
                    this.paramsFEI = {
                        feiId: 'FEI.LINK.GET',
                        fiscalCode: cf,
                        feiRequestPayload: node.params
                            ? node.params
                            : null,
                        feiLink: node.redirectLink
                    };
                } else if (node.requestType === 'POST') {
                    const request = {
                        properties: node.params ? JSON.parse(node.params) : null,
                        requestUrl: node.redirectLink,
                        requestMethod: node.requestType
                    };

                    this.paramsFEI = {
                        feiId: 'FEI.LINK.POST',
                        fiscalCode: cf,
                        feiRequestPayload: JSON.stringify(request)
                    };
                } else {
                    throw new Error(`Unsupported requestType: ${node.requestType}`);
                }
            }

            console.log(`paramsFEI:: ${JSON.stringify(this.paramsFEI)}`, JSON.stringify({paramsFEI: this.paramsFEI,node}));
            this.isLWCModalOpened = true;                       // apre la modal
        } catch (err) {
            console.error(
                `MenuStrumentiContainer::handleNavigateFei:: ${JSON.stringify(err)}`
            );
            this.sendAlert('error', 'Errore nella preparazione della richiesta FEI');
        } finally {
            this.hideLoader();
            console.log('MenuStrumentiContainer::handleNavigateFei finished');
        }
    }


    sendAlert(type, message) {
        const evt = new ShowToastEvent({
            title: '',
            message: message,
            variant: type,
        });
        this.dispatchEvent(evt);
    }
}