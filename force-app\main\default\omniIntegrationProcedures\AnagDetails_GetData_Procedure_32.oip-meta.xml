<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;recordId&quot;: &quot;a1i9X0000070FUTQA2&quot;,
    &quot;UserId&quot;: &quot;0059X00000PrV0GQAV&quot;
}</customJavaScript>
    <description>Stefano Fusco: inserimento UserInfo nel nodo &quot;infoSocietarie&quot;</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>AnagraficaDetails</name>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>AccAccRelAgency</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;RelatedId&quot; : &quot;=%AnagDetailExtract:accountAccountRelationAgency_Id%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues40&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>12.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>AccAccRelSociety</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;RelatedId&quot; : &quot;=%AnagDetailExtract:accountAccountRelationSociey_Id%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues40&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>AnagDetailExtract</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;AnagDetailsExtractData&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;UserId&quot;,
    &quot;element&quot; : &quot;UserId&quot;
  }, {
    &quot;inputParam&quot; : &quot;AccountId&quot;,
    &quot;element&quot; : &quot;RetriveRecTypeFromAccDetail:account_Id&quot;
  }, {
    &quot;inputParam&quot; : &quot;SocietaId&quot;,
    &quot;element&quot; : &quot;RetriveRecTypeFromAccDetail:society_Id&quot;
  }, {
    &quot;inputParam&quot; : &quot;RecTypeAccAgencyId&quot;,
    &quot;element&quot; : &quot;ANAGRetriveRT:accountAgencyDetails_recTypeId&quot;
  }, {
    &quot;inputParam&quot; : &quot;RecTypeId&quot;,
    &quot;element&quot; : &quot;ANAGRetriveRT:accountDetails_recTypeId&quot;
  }, {
    &quot;inputParam&quot; : &quot;RecTypeMDM&quot;,
    &quot;element&quot; : &quot;ANAGRetriveRT:accountDetailsMDM_recTypeId&quot;
  }, {
    &quot;inputParam&quot; : &quot;RecTypePA&quot;,
    &quot;element&quot; : &quot;ANAGRetriveRT:accountDetailsPrivateArea_recTypeId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction6&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>AnagDetailExtractCompagnie</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;SocietaId&quot; : &quot;%RetriveRecTypeFromAccDetail:society_Id%&quot;,
    &quot;AccountId&quot; : &quot;%RetriveRecTypeFromAccDetail:account_Id%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;Util_GetAltreSocieta&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction6&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>AnagDetailExtractUserInfos</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;UserInfo&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UtilGetUserInfo&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;UserId&quot;,
    &quot;element&quot; : &quot;UserId&quot;
  }, {
    &quot;inputParam&quot; : &quot;RecordId&quot;,
    &quot;element&quot; : &quot;recordId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ANAGRetriveRT</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;ANAGRetriveRecordType&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;RecTypeInput&quot;,
    &quot;element&quot; : &quot;RecTypeAcc:Value&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>FormatResponsePG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;company&quot; : &quot;=%RetriveRecTypeFromAccDetail:Compagnia%&quot;,
    &quot;type&quot; : &quot;=&apos;Business&apos;&quot;,
    &quot;tipoFormaGiuridica&quot; : &quot;=%RetriveRecTypeFromAccDetail:accountDetail_ProfessionLegalEntityType%&quot;
  },
  &quot;additionalOutput&quot; : {
    &quot;TPG&quot; : &quot;=QUERY(\&quot;SELECT ProfessionLegalEntityType__c FROM AccountDetails__c WHERE Id =  &apos;{0}&apos;\&quot;, %recordId% )&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;%RespPreTransformBusiness%&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;AnagDetailTransformPG&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetCodiceAttivitaData</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;RecordId&quot; : &quot;=recordId&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;CodiceAttivita_GetData&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetFormaSocietaria</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;FormaSocietaria&quot; : &quot;&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;RestGet_TipoFormaSocietaria&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction7&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetInfoSocietarie</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;RecordId&quot; : &quot;=%recordId%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;RestGet_DatiSocietari&quot;,
  &quot;remoteOptions&quot; : {
    &quot;&quot; : &quot;&quot;
  },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction8&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>InfoSocNode</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;Attivita&quot; : &quot;=%GetCodiceAttivitaData%&quot;,
    &quot;SitoWeb&quot; : &quot;=%GetInfoSocietarie:sito%&quot;,
    &quot;InfoSocietarie&quot; : &quot;=%GetInfoSocietarie:response%&quot;,
    &quot;Ciu&quot; : &quot;=%AnagDetailExtract:accountDetail_Ciu%&quot;,
    &quot;Compagnia&quot; : &quot;=%AnagDetailExtract:accountDetail_compagnia%&quot;,
    &quot;TipoInserimento&quot; : &quot;=%GetInfoSocietarie:tipo%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues9&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseAction2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;company&quot; : &quot;=%RetriveRecTypeFromAccDetail:Compagnia%&quot;,
    &quot;type&quot; : &quot;=&apos;Business&apos;&quot;,
    &quot;recapiti:inserisci:PropOptions&quot; : &quot;=IF((COUNTQUERY(\&quot;SELECT COUNT() FROM FinServ__AccountAccountRelation__c WHERE FinServ__Account__c = &apos;{0}&apos; and FinServ__Role__r.FinServ__InverseRole__c = &apos;Compagnia&apos;\&quot;, %RetriveRecTypeFromAccDetail:account_Id%) == 1),\&quot;\&quot;,LIST(%AnagDetailExtractCompagnie%))&quot;,
    &quot;recapiti:inserisci:DisableCompany&quot; : &quot;=$Vlocity.true&quot;,
    &quot;recapiti:inserisci:DisablePropagation&quot; : &quot;=(COUNTQUERY(\&quot;SELECT COUNT() FROM FinServ__AccountAccountRelation__c WHERE FinServ__Account__c = &apos;{0}&apos; and FinServ__Role__r.FinServ__InverseRole__c = &apos;Compagnia&apos;\&quot;, %RetriveRecTypeFromAccDetail:account_Id%) == 1)&quot;,
    &quot;datiAnagrafici:formaSoc&quot; : &quot;=FILTER(LIST(%GetFormaSocietaria%), &apos;codice == \&quot;&apos; + %RespPreTransform:Value:accountDetail_CompanyType% + &apos;\&quot;&apos;)&quot;,
    &quot;datiAnagrafici:TipoPersGiuridica&quot; : &quot;=QUERY(\&quot;SELECT Valore__c FROM AnagMappingCodDesc__mdt WHERE Field__c = &apos;TipoPersonaGiuridica&apos; AND Codice__c = &apos;{0}&apos;\&quot;, QUERY(\&quot;SELECT ProfessionLegalEntityType__c FROM AccountDetails__c WHERE Id =  &apos;{0}&apos;\&quot;, %recordId% ) )&quot;,
    &quot;datiAnagrafici:isCorp&quot; : &quot;=IF(QUERY(\&quot;SELECT IsCorporate__c FROM AccountDetails__c WHERE Id =  &apos;{0}&apos;\&quot;, %recordId% ), \&quot;Si\&quot;,\&quot;No\&quot;)&quot;,
    &quot;datiAnagrafici:STatoPartitaIVA&quot; : &quot;=QUERY(\&quot;SELECT VatNumberStatus__c FROM AccountDetails__c WHERE Id =  &apos;{0}&apos;\&quot;, %recordId% )&quot;,
    &quot;datiAgenzia:Modifica:CIPList&quot; : &quot;=%GetCIPList:CIPList%&quot;,
    &quot;datiAgenzia:Modifica:AccAgeDetId&quot; : &quot;=%AnagDetailExtract:accountAgencyDetail_id%&quot;,
    &quot;datiAgenzia:Modifica&quot; : &quot;=%UserInfo%&quot;,
    &quot;recapiti:company&quot; : &quot;=%RetriveRecTypeFromAccDetail:Compagnia%&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:agenzia&quot; : &quot;=&apos;%UserInfo:codiceAgenzia%&apos;&quot;,
    &quot;recapiti:checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:userInfo&quot; : &quot;=%UserInfo%&quot;,
    &quot;recapiti:inserisci:userInfo&quot; : &quot;=%UserInfo%&quot;,
    &quot;recapiti:inserisci:RecordType&quot; : &quot;=&apos;Business&apos;&quot;,
    &quot;datiAnagrafici:modifica:indirizzi|2:tipoIndirizzo&quot; : &quot;=&apos;SEOP&apos;&quot;,
    &quot;datiAnagrafici:modifica:indirizzi|2:distrettoCensuario&quot; : &quot;=&apos;&apos;&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:codiceFiscale&quot; : &quot;=TOSTRING(AnagDetailExtract:account_ExternalId)&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:sesso&quot; : &quot;=null&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:tipoAnagraficaRichiesto&quot; : &quot;=&apos;D&apos;&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:nome&quot; : &quot;=null&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:ragioneSociale&quot; : &quot;=AnagDetailExtract:accountDetail_FullName&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:tipoAnagrafica&quot; : &quot;=null&quot;,
    &quot;recapiti:inserisci:checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:cognome&quot; : &quot;=null&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:partitaIVA&quot; : &quot;=TOSTRING(AnagDetailExtract:account_ExternalId)&quot;,
    &quot;datiAnagrafici:modifica:professione&quot; : &quot;=FormatProfessione&quot;,
    &quot;datiAnagrafici:ClientePressoAltraAgenzia&quot; : &quot;=IF(%clienteAltraAgenzia:isClienteAltraAgenzia%,\&quot;SI\&quot;,\&quot;NO\&quot;)&quot;,
    &quot;datiAnagrafici:StatoSoggetto&quot; : &quot;=IF(ISBLANK(%KPI:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO%), \&quot;Prospect\&quot;, %KPI:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value%)&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:formaSocietaria&quot; : &quot;=FILTER(LIST(%GetFormaSocietaria%), &apos;codice == \&quot;&apos; + %RespPreTransform:Value:accountDetail_CompanyType% + &apos;\&quot;&apos;)&quot;,
    &quot;datiAnagrafici:nascondiProfessione&quot; : &quot;=(%FormatResponsePG:TPG% == \&quot;SPF\&quot;) ||\r\n(%FormatResponsePG:TPG% == \&quot;SCP\&quot;) ||\r\n(%FormatResponsePG:TPG% == \&quot;CON\&quot;) ||\r\n(%FormatResponsePG:TPG% == \&quot;ERE\&quot;) ||\r\n(%FormatResponsePG:TPG% == \&quot;ATI\&quot;) ||\r\n(%FormatResponsePG:TPG% == \&quot;APS\&quot;) ||\t\r\n(%FormatResponsePG:TPG% == \&quot;CPR\&quot;) ||\r\n(%FormatResponsePG:TPG% == \&quot;BAN\&quot;) &quot;,
    &quot;infoSocietarie:inserisci:userInfo&quot; : &quot;=%UserInfo%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;FormatResponsePG&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>7.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>RespPreTransformBusiness</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;KPI&quot; : &quot;=%KPI%&quot;,
    &quot;Value&quot; : &quot;=%AnagDetailExtract%&quot;,
    &quot;Indirizzi&quot; : &quot;=%IndList%&quot;,
    &quot;Professione&quot; : &quot;=%FormatProfessione%&quot;,
    &quot;Privacy&quot; : &quot;=%FormatPrivacy%&quot;,
    &quot;InfoSocietarie&quot; : &quot;=%InfoSocNode%&quot;,
    &quot;CompagnieMandato&quot; : &quot;=%AnagDetailExtractCompagnie%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues43&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>BusinessResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RecTypeAcc:Value% == &apos;Business&apos;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>27.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>CheckClienteAltraAgenzia</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;society&quot; : &quot;=QUERY(\&quot;SELECT ExternalId__c FROM Account WHERE id = &apos;{0}&apos;\&quot;,  %SetCCAA2:societyId%)&quot;,
    &quot;accountId&quot; : &quot;=%SetCCAA2:accountId%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;clienteAltraAgenzia&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;checkClienteAltraAgenzia&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;RemoteAction3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Remote Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetCCAA</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;aarSociety&quot; : &quot;=QUERY(\&quot;SELECT Relation__c FROM AccountDetails__c WHERE id = &apos;{0}&apos;\&quot;,  %recordId%)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues9&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetCCAA2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;accountId&quot; : &quot;=QUERY(\&quot;SELECT FinServ__Account__c FROM FinServ__AccountAccountRelation__c WHERE id = &apos;{0}&apos;\&quot;,  %SetCCAA:aarSociety%)&quot;,
    &quot;societyId&quot; : &quot;=QUERY(\&quot;SELECT FinServ__RelatedAccount__c FROM FinServ__AccountAccountRelation__c WHERE id = &apos;{0}&apos;\&quot;,  %SetCCAA:aarSociety%)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues10&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckClienteAltraAgenziaBlock</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>FormatIndDom</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;%AnagDetailExtract%&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;AnagIndDom&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>15.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>FormatIndRes</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;%AnagDetailExtract%&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;AnagIndRes&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>16.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>FormatPrivacy</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;privacy&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;GetPrivacy&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;AnagPrivacy&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction8&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>22.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>FormatProfessione</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;professione&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;%AnagDetailExtract%&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;AnagProfessione&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction7&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>24.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>FormatRecCell</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;%AnagDetailExtract%&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;AnagRecCell&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction6&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>19.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>FormatRecEmail</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;%AnagDetailExtract%&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;AnagRecEmail&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>18.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetCIPList</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;UserId&quot; : &quot;=%UserId%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;Util_GetCipData&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction9&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>23.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetPermissionInfo</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;customPermissions&quot; : [ &quot;CancellazioneRecapiti&quot;, &quot;TabsUnipolSai&quot;, &quot;TabsUniSalute&quot; ]
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;checkCp&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;checkCustomPermissions&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;RemoteAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetPrivacy</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;ciu&quot; : &quot;%AnagDetailExtract:accountDetail_Ciu%&quot;,
    &quot;compagnia&quot; : &quot;=IF(AnagDetailExtract:accountDetail_compagnia = &apos;UNIPOL&apos;, &apos;unipolsai&apos;, IF( AnagDetailExtract:accountDetail_compagnia = &apos;UNISALUTE&apos;, &apos;unisalute&apos;, AnagDetailExtract:accountDetail_compagnia))&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;RestGet_Privacy&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>21.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>FormatResponse</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;company&quot; : &quot;=%RetriveRecTypeFromAccDetail:Compagnia%&quot;,
    &quot;type&quot; : &quot;=&apos;Individual&apos;&quot;,
    &quot;GetBirthCityName:descrizione&quot; : &quot;=QUERY(\&quot;SELECT Name FROM Unita_Territoriali__c  WHERE Codice_catasto__c = &apos;{0}&apos; and  RecordType.Name = &apos;Comune&apos; LIMIT 1 \&quot;, %RespPreTransform:Value:accountDetail_BirthPlace%)&quot;,
    &quot;GetBirthCountryName:descrizione&quot; : &quot;=QUERY(\&quot;SELECT Name FROM Unita_Territoriali__c  WHERE Codice_ABI__c = &apos;{0}&apos; and  RecordType.Name = &apos;Nazione&apos; LIMIT 1 \&quot;, %RespPreTransform:Value:accountDetail_BirthCountry%)&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;%RespPreTransform%&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;AnagDetailTransform&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetBirthCityName</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RespPreTransform:Value:accountDetail_BirthPlace% &lt;&gt; \&quot;-\&quot;\r\n&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;codiceBelfiore&quot; : &quot;=%RespPreTransform:Value:accountDetail_BirthPlace%&quot;,
    &quot;limiteOccorrenze&quot; : &quot;=1&quot;,
    &quot;soloAttivi&quot; : &quot;=&apos;false&apos;&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;RestGet_Comuni&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetBirthCountryName</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RespPreTransform:Value:accountDetail_BirthCountry% &lt;&gt; \&quot;-\&quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;filtroGeografico&quot; : &quot;=&apos;DISATTIVO&apos;&quot;,
    &quot;codiceABI&quot; : &quot;=%RespPreTransform:Value:accountDetail_BirthCountry%&quot;,
    &quot;soloAttivi&quot; : &quot;=&apos;false&apos;&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;RestGet_Stati&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseAction1</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;company&quot; : &quot;=%RetriveRecTypeFromAccDetail:Compagnia%&quot;,
    &quot;type&quot; : &quot;=&apos;Individual&apos;&quot;,
    &quot;recapiti:inserisci:PropOptions&quot; : &quot;=IF((COUNTQUERY(\&quot;SELECT COUNT() FROM FinServ__AccountAccountRelation__c WHERE FinServ__Account__c = &apos;{0}&apos; and FinServ__Role__r.FinServ__InverseRole__c = &apos;Compagnia&apos;\&quot;, %RetriveRecTypeFromAccDetail:account_Id%) == 1),\&quot;\&quot;,LIST(%AnagDetailExtractCompagnie%))&quot;,
    &quot;recapiti:inserisci:DisableCompany&quot; : &quot;=$Vlocity.true&quot;,
    &quot;recapiti:inserisci:DisablePropagation&quot; : &quot;=(COUNTQUERY(\&quot;SELECT COUNT() FROM FinServ__AccountAccountRelation__c WHERE FinServ__Account__c = &apos;{0}&apos; and FinServ__Role__r.FinServ__InverseRole__c = &apos;Compagnia&apos;\&quot;, %RetriveRecTypeFromAccDetail:account_Id%) == 1)&quot;,
    &quot;datiAgenzia:Modifica:CIPList&quot; : &quot;=%GetCIPList:CIPList%&quot;,
    &quot;datiAgenzia:Modifica:AccAgeDetId&quot; : &quot;=%AnagDetailExtract:accountAgencyDetail_id%&quot;,
    &quot;datiAgenzia:Modifica&quot; : &quot;=%UserInfo%&quot;,
    &quot;recapiti:company&quot; : &quot;=%RetriveRecTypeFromAccDetail:Compagnia%&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:compagnia&quot; : &quot;==IF(%RetriveRecTypeFromAccDetail:Compagnia% = &apos;UNIPOL&apos;, &apos;unipolsai&apos;, IF(%RetriveRecTypeFromAccDetail:Compagnia% = &apos;UNISALUTE&apos;, &apos;unisalute&apos;, %RetriveRecTypeFromAccDetail:Compagnia%))&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:agenzia&quot; : &quot;=&apos;%UserInfo:codiceAgenzia%&apos;&quot;,
    &quot;recapiti:checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;datiAnagrafici:modifica:anagrafica:userInfo&quot; : &quot;=%UserInfo%&quot;,
    &quot;recapiti:inserisci:userInfo&quot; : &quot;=%UserInfo%&quot;,
    &quot;recapiti:inserisci:checkCp&quot; : &quot;%checkCp%&quot;,
    &quot;datiAnagrafici:ClientePressoAltraAgenzia&quot; : &quot;=IF(%clienteAltraAgenzia:isClienteAltraAgenzia%,\&quot;SI\&quot;,\&quot;NO\&quot;)&quot;,
    &quot;datiAnagrafici:StatoSoggetto&quot; : &quot;=IF(ISBLANK(%KPI:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO%), \&quot;Prospect\&quot;, %KPI:AccountKeyAgency:PP_AGENZIA_SOCIETA_STATO:Value%)&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;FormatResponse&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>IndividualResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%RecTypeAcc:Value% == &apos;Individual&apos;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>26.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>IndList</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;advancedMerge&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;preventIntraListMerge&quot; : false,
  &quot;mergeFields&quot; : [ ],
  &quot;allowMergeNulls&quot; : true,
  &quot;hasPrimary&quot; : false,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ ],
  &quot;sortInDescendingOrder&quot; : false,
  &quot;mergeListsOrder&quot; : [ &quot;FormatIndRes&quot;, &quot;FormatIndDom&quot; ],
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ListAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>17.0</sequenceNumber>
        <type>List Merge Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RecList</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;advancedMerge&quot; : false,
  &quot;advancedMergeMap&quot; : [ ],
  &quot;preventIntraListMerge&quot; : false,
  &quot;mergeFields&quot; : [ ],
  &quot;allowMergeNulls&quot; : true,
  &quot;hasPrimary&quot; : false,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;sortBy&quot; : [ ],
  &quot;sortInDescendingOrder&quot; : false,
  &quot;mergeListsOrder&quot; : [ &quot;FormatRecEmail&quot;, &quot;FormatRecCell&quot; ],
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;updateFieldValue&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ListAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>20.0</sequenceNumber>
        <type>List Merge Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RecType</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;Value&quot; : &quot;=%ANAGRetriveRT:accountDetails_recTypeId%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues41&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RecTypeAcc</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;Value&quot; : &quot;=%RetriveRecTypeFromAccDetail:accountDetail_RecTypeDevName%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues42&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RecTypeAccAgency</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;Value&quot; : &quot;=%ANAGRetriveRT:accountAgencyDetails_recTypeId%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues41&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RespPreTransform</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;KPI&quot; : &quot;=%KPI%&quot;,
    &quot;Value&quot; : &quot;=%AnagDetailExtract%&quot;,
    &quot;Indirizzi&quot; : &quot;=%IndList%&quot;,
    &quot;Professione&quot; : &quot;=%FormatProfessione%&quot;,
    &quot;Privacy&quot; : &quot;=%FormatPrivacy%&quot;,
    &quot;Attivita&quot; : &quot;=%GetCodiceAttivitaData%&quot;,
    &quot;CompagnieMandato&quot; : &quot;=%AnagDetailExtractCompagnie%&quot;,
    &quot;Contatti&quot; : &quot;=%RecList%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues43&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>25.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RetrieveKPIFromAsset</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;aarSociety&quot; : &quot;=%AccAccRelSociety:RelatedId%&quot;,
    &quot;aarAgency&quot; : &quot;=%AccAccRelAgency:RelatedId%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;KPI&quot;,
  &quot;responseJSONNode&quot; : &quot;KPI&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;KPIAnag&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;RemoteAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>13.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RetriveKPI</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;RelatedIdSociety&quot; : &quot;=%AccAccRelSociety:RelatedId%&quot;,
    &quot;RelatedIdAgency&quot; : &quot;=%AccAccRelAgency:RelatedId%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;Anag_GetKpi&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>14.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RetriveRecTypeFromAccDetail</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;AnagDetailsRetriveRecType&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;recordId&quot;,
    &quot;element&quot; : &quot;recordId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessKey>AnagDetails_GetData</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { }
}</propertySetConfig>
    <subType>GetData</subType>
    <type>AnagDetails</type>
    <uniqueName>AnagDetails_GetData_Procedure_32</uniqueName>
    <versionNumber>32.0</versionNumber>
    <webComponentKey>9000178f-6225-ba49-248f-1a413c1fe4e7</webComponentKey>
</OmniIntegrationProcedure>
