import { LightningElement, wire, track, api } from 'lwc';
import { getRecord, getFieldValue } from 'lightning/uiRecordApi';
import { EnclosingTabId, getTabInfo, openSubtab } from 'lightning/platformWorkspaceApi'; // Import methods from workspace API
import STAGENAME_FIELD from '@salesforce/schema/Opportunity.StageName';
import DOCUMENT_URL_FIELD from '@salesforce/schema/Quote.DocumentURL__c';
import edit_icon from '@salesforce/resourceUrl/EditIcon'; // Import resource URL for edit icon
import { publish, MessageContext } from 'lightning/messageService';
import FEI_CHANNEL from '@salesforce/messageChannel/FeiContainerChannel__c';
import getPermissionSetName from '@salesforce/apex/FeiHelper.getPermissionSetName';
import getFederationIdentifier from '@salesforce/apex/FeiHelper.getFederationIdentifier';
import getCustomerAndAgencyIds from '@salesforce/apex/FeiHelper.getCustomerAndAgencyIds';

const QUOTE_FIELDS = [DOCUMENT_URL_FIELD];
const OPPORTUNITY_FIELDS = [ STAGENAME_FIELD ];
const EDIT_ICON = edit_icon + '/edit.png'; // Define the URL for the edit icon
const ESSIG_DOMAINS = new Set(['ESSIG_AUTO','ESSIG_RE','ESSIG_VITA','ESSIG_VITA_PREVIDENZIALE','ESSIG_VITA_INDIVIDUALE']);
const PREVENTIVI_FLOW = 'FEIQuickActionPreventivi';
const FEI_PREVENTIVI_VIEW = {
    ESSIG_AUTO:            'IP.CRUSCOTTO.DISPATCH',
    ESSIG_RE :'RE.CRUSCOTTO.DISPATCH',
    ESSIG_VITA :            'VITA.PROPOSTA',
    ESSIG_VITA_PREVIDENZA:       'IP.CRUSCOTTO.DISPATCH',
    ESSIG_VITA_INDIVIDUALE:       'VITA.PROPOSTA'
};
const TITLES_PREVENTIVI_VIEW = {
    ESSIG_AUTO:            'Visualizza preventivo',
    ESSIG_RE :'Visualizza preventivo',
    ESSIG_VITA :            'Visualizza preventivo',
    ESSIG_VITA_PREVIDENZA:       'Visualizza preventivo',
    ESSIG_VITA_INDIVIDUALE:       'Visualizza preventivo',
};

const FEI_PREVENTIVI_MODIFY = {
    ESSIG_AUTO:            'NPAC.CRUSCOTTO.DISPATCH',
    ESSIG_RE :null,
    ESSIG_VITA :            'VITA.PROPOSTA',
    ESSIG_VITA_PREVIDENZA:       'RE.CRUSCOTTO.DISPATCH',
    ESSIG_VITA_INDIVIDUALE:       'VITA.PROPOSTA'
};
const TITLES_PREVENTIVI_MODIFY = {
    ESSIG_AUTO:            'Definisci in Polizza',
    ESSIG_RE : 'Modifica preventivo',
    ESSIG_VITA :            'Modifica preventivo',
    ESSIG_VITA_PREVIDENZA:       'Riprendi preventivo',
    ESSIG_VITA_INDIVIDUALE:       'Riprendi preventivo'
};



// Define the AccordionHeader class that extends LightningElement
export default class AccordionHeader extends LightningElement {
    @api opportunityRecordId; // Public property to hold the opportunity record ID
    @track subject = 'Modifica preventivo esistente'; // Trackable property to hold the subject

    @api quote; // Public property to hold the quote data
    @api sourceC; // Parent opportunity source channel
    @track showPreview;
    @track showFEI = false;
    flowName = "FEIQuickActionUNICA";
    @track flowTitle = "Modifica Preventivo";
    @track flowTitleModifica = "Modifica Preventivo";
    @track flowTitleVisualizza = "Visualizza Preventivo";

    @track flowInputVariables = [];

    previewTitle = 'Anteprima Preventivo';
    @track isFeiTypeModify=false;
    @track feiTypeModify = 'MODIFICA_PREVENTIVO';
    @track feiTypeView = 'CONSULTAZIONE_CONTRATTO';
    @track feiTypeValueParam = 'CONSULTAZIONE_CONTRATTO';

@wire(MessageContext) messageContext;
@wire(getRecord, { recordId: '$opportunityRecordId', fields: OPPORTUNITY_FIELDS })
  wiredOpp;
  @wire(getRecord, {
    recordId: '$quote.recordId',
    fields: QUOTE_FIELDS
  })
  wiredQuote;

  get opportunityStageName() {
    return getFieldValue(this.wiredOpp.data, STAGENAME_FIELD);
  }

//@wire(MessageContext)
  //messageContext;

renderedCallback() {
  console.log('🗒️ quote completo:', JSON.stringify(this.quote));
  console.log('🎯 documentUrl wired:', this.documentUrl);
}


    connectedCallback()
    {
        console.log('data domain figlio: '+JSON.stringify(this.quote));
        this.flowInputVariables =
        [{
            name: 'recordId',
            type: 'String',
            value: this.quote.recordId
        },{
            name: 'feiType',
            type: 'String',
            value: this.feiTypeValueParam
        }];
    }

    get ramoKey() {
        return (this.quote.domainType || '').trim().toUpperCase();
    }

    get unicaLabel() {
        console.log('Domain Type retrieved: '+this.quote.domainType)
        return ESSIG_DOMAINS.has(this.quote.domainType?.trim()?.toUpperCase()) ? 'ESSIG' : 'UNICA';
    }
get isPrevidenza() {
    if (this.sourceC === undefined) {
        this.sourceC = this.source;
    }
    return this.sourceC === 'Preventivatore Previdenza';
   }
    // Getter to determine if the opportunity is closed
    get isOpportunityClosed() { 
        return this.quote.isOpportunityClosed; 
    }

    //Getter to hide or not the quote utility icon (edit)
    get hideQuoteUtilityEdit() { //Guy De Roquefeuil: 19-05-25 // SC: 26-05-25
        return (this.quote.isSold || this.quote.isExpired) || this.quote.isOpportunityAssignedOrBefore;
    }
    //Getter to hide or not the quote utility icon (PDF download)
    get hideQuoteUtilityPDF() { //Guy De Roquefeuil: 19-05-25 // SC: 26-05-25
        return this.quote.isExpired || this.quote.isOpportunityAssignedOrBefore;
    }
    // Getter to determine if the header title should be displayed
    get displayHeaderTitle() { 
        return this.quote.isFirst; 
    }

    // Getter to retrieve the quote name
    get name() { 
        return this.quote.name; 
    }

    // Getter to retrieve the quote record ID
    get recordId() { 
        return this.quote.recordId; 
    }

    // Getter to retrieve the images for areas of need
    get areasOfNeedImages() { 
        console.log('immagine:' +this.quote.areasOfNeedImages)
        return this.quote.areasOfNeedImages; 
    }

    // Getter to retrieve the total amount of the quote
    get totalAmount() { 
        return this.quote.totalAmount; 
    }

    // Getter to retrieve the status of the quote
    get status() { 
        return this.quote.status; 
    }

    // Getter to retrieve the source of the quote //SC 09-04-25 OCT 1289109
    get source() { 
        return this.quote.source; 
    }

    // Getter to retrieve the digitalStep of the quote //SC 09-04-25 OCT 1289109
    get digitalStep() { 
        return this.quote.digitalStep; 
    }

    // Getter to retrieve the cip of the quote //SC 09-04-25 OCT 1289109
    get cip() { 
        return this.quote.cip; 
    }

    // Getter to retrieve the creation date of the quote
    get creationDate() { 
        return this.quote.creationDate; 
    }

    // Getter to retrieve the expiration date of the quote
    get expirationDate() { 
        return this.quote.expirationDate; 
    }

    // Getter to retrieve the unica link for the quote
    get unicaLink() { 
        return this.quote.unicaLink; 
    }

    // Getter to retrieve the coverages of the quote
    get opportunityCoverages() { 
        return this.quote.opportunityCoverages; 
    }

    get monthlyContribution()
    { 
        return this.quote.monthlyContribution; 
    }

    /*get documentUrl()
    {
        return this.quote.documentUrl;
    }*/

      get documentUrl() {
    return getFieldValue(this.wiredQuote.data, DOCUMENT_URL_FIELD);
  }

    get domainType()
    {
        return this.quote.domainType;
    }

get hasDocument() {
    const docUrl = this.documentUrl;;
    const hasDoc = !!docUrl;
    console.log('⚙️ hasDocument getter – documentUrl:', docUrl, '→ hasDocument:', hasDoc);
    return hasDoc;
}
// inside AccordionHeader class
  get showPdfLink() {
    const hasDoc    = this.hasDocument;
    const stage     = this.opportunityStageName?.trim();
    const inGest    = stage === 'In gestione';
    const closed    = stage === 'Chiuso';
console.log(
    '🔎 showPdfLink →',
    'hasDoc =', hasDoc,
    '| stage =', stage,
    '| inGest =', inGest
  );

  // oppure, con template string:
  console.log(`🔎 showPdfLink → hasDoc=${hasDoc}, stage=${stage}, inGest=${inGest}`);
  return hasDoc && (inGest || closed);
  }

// dentro AccordionHeader
 get showAccediLink() {
    const dt = (this.quote.domainType || '').trim().toUpperCase();
    const excluded = ['ESSIG_VITA_PREVIDENZA', 'ESSIG_VITA_INDIVIDUALE', 'ESSIG_VITA_PREVIDENZA_SIM'];
    const stage = this.opportunityStageName?.trim();
    const inGest = stage === 'In gestione';

    return !excluded.includes(dt) && inGest;
}



    // Getter to retrieve the URL for the edit icon
    get edit_icon() { 
        return EDIT_ICON; 
    }

    @wire (EnclosingTabId) tabId; // Wire property to get the enclosing tab ID

    // Method to handle a click on the quote name
    async handleNameClick() {
        if (!this.tabId) {
            return;
        }

        const tabInfo = await getTabInfo(this.tabId); // Get information about the enclosing tab
        const primaryTabId = tabInfo.isSubtab ? tabInfo.parentTabId : tabInfo.tabId; // Determine the primary tab ID

        await openSubtab(primaryTabId, { recordId: this.recordId, focus: true }); // Open a subtab with the quote record ID
    }

    handleClose()
    {
        this.showFEI = false;
    }

    // Method to handle a click on the "Modifica" button
    /*handleModificaClick() {
        this.isFeiTypeModify=true; 
        this.invokeFlow(); // Uncomment to start the flow
    }*/

    // Method to handle a click on the "Visualizza" button
    handleVisualizzaClick() {
        this.isFeiTypeModify=false; 
        this.invokeFlow(); // Uncomment to start the flow
    }

    handleStatusChange(event)
    {
        if (event.detail.status === 'FINISHED') {
            this.showFEI = false;
        }
    }
    invokeFlow()
    {
        if(this.isFeiTypeModify){
            this.flowInputVariables =
                [{
                    name: 'recordId',
                    type: 'String',
                    value: this.quote.recordId
                },{
                    name: 'feiType',
                    type: 'String',
                    value: this.feiTypeModify
                }];
            this.flowTitle=this.flowTitleModifica;
        }else{
                this.flowInputVariables =
                    [{
                        name: 'recordId',
                        type: 'String',
                        value: this.quote.recordId
                    },{
                        name: 'feiType',
                        type: 'String',
                        value: this.feiTypeView
                    }];
                    this.flowTitle=this.flowTitleVisualizza;
            }
        console.log(JSON.stringify(this.flowInputVariables));
        this.showFEI = true;
    }

    @track showContent = false; // Trackable property to determine if the content is shown
    @track buttonIcon = 'utility:chevronright'; // Trackable property to determine the icon for the toggle button

   
    toggleContent() {
        this.showContent = !this.showContent; // Toggle the showContent property
        
        this.buttonIcon = this.showContent ? 'utility:chevrondown' : 'utility:chevronright'; // Update the button icon based on the content visibility
    }


 handleVisualizzaClickPDF() {
        console.log('handleVisualizzaClickPDF: domainType=', this.quote.domainType);
        const map = this.isFeiTypeModify ? FEI_PREVENTIVI_MODIFY : FEI_PREVENTIVI_VIEW;
        const titles = this.isFeiTypeModify ? TITLES_PREVENTIVI_MODIFY : TITLES_PREVENTIVI_VIEW;
        const key = this.ramoKey;
        console.log('handleVisualizzaClickPDF: ramoKey=', key);
        if (!map.hasOwnProperty(key)) {
            console.warn(`Ambito FEI non valido: "${key}"`);
            return;
        }
        const feiid = map[key];
        const title = titles[key];
        console.log('handleVisualizzaClickPDF: feiid=', feiid, ' title=', title);
        this.flowName = PREVENTIVI_FLOW;
        this.flowTitle = title;
        this.flowInputVariables = [
            { name: 'recordId', type: 'String', value: this.quote.recordId },
            { name: 'FEIID', type: 'String', value: feiid }
        ];
    if (key === 'ESSIG_VITA_INDIVIDUALE') {
        this.flowInputVariables.push({ name: 'society', type: 'String', value: 'SOC_1' });
        console.log('launchPreventivi: aggiunto parametro source = SOC_1');
    }
        console.log('handleVisualizzaClickPDF: flowInputVariables=', JSON.stringify(this.flowInputVariables));
        this.showFEI = true;
    }



    launchPreventivi(map, titles) {
        const key = this.ramoKey;
        console.log('launchPreventivi: ramoKey=', key);
        if (!map.hasOwnProperty(key)) {
            console.warn(`Ambito FEI non valido: "${key}"`, ' available keys:', Object.keys(map));
            return;
        }
        const feiid = map[key];
        const title = titles[key];
        console.log('launchPreventivi: feiid=', feiid, ' title=', title);
        if (!feiid) {
            console.warn(`FEIID non disponibile per ramo "${key}"`);
            return;
        }
        this.flowName = PREVENTIVI_FLOW;
        this.flowTitle = title;
        this.flowInputVariables = [
            { name: 'recordId', type: 'String', value: this.quote.recordId },
            { name: 'FEIID', type: 'String', value: feiid }
        ];
        console.debug('launchPreventivi: flowInputVariables=', JSON.stringify(this.flowInputVariables));
        this.showFEI = true;
    }


async myPDFPreview() {
  const dt     = this.quote.domainType?.trim().toUpperCase();
  const hasDoc = !!this.documentUrl;

  // PDF UNICA
  if ((dt === 'PU' || dt === 'ESSIG_RE') && hasDoc) {
    this.openPDFPreview();
    }
  // ESSIG_AUTO → LMS
     else if (dt === 'ESSIG_AUTO') {
            try {
                const [fiscalCode, permissionSetName] = await Promise.all([
                    getFederationIdentifier(),
                    getPermissionSetName()
                ]);
                const { customerId, agencyId } = await getCustomerAndAgencyIds({ quoteId: this.quote.recordId });
                const feiRequestPayload = {
                    tipoOperazione: 'SS',
                    compagnia:      customerId,
                    agenzia:        agencyId,
                    progressivo:    '1',
                    numeroArchivio: this.quote.SourceSystemIdentifier0Formula__c || '',
                    numeroVersione: this.quote.SourceSystemIdentifier1Formula__c || ''
                };
                const payload = {
                    recordId:          this.quote.recordId,
                    FEIID:             'RA.STAMPA.DIFFERITA',
                    FiscalCode:        fiscalCode,
                    permissionSetName: permissionSetName,
                    society:           'SOC_1',
                    feiRequestPayload
                };
                console.log('📤 ESSIG_AUTO LMS payload:', JSON.stringify(payload, null, 2));
                publish(this.messageContext, FEI_CHANNEL, payload);
                console.log('📤 Published ESSIG_AUTO LMS message');
            } catch (err) {
                console.error('Errore FEI ESSIG_AUTO:', err);
                this.dispatchEvent(new ShowToastEvent({ title: 'Errore FEI', message: err.body?.message||err.message, variant: 'error' }));
            }
        }
    else {
      this.handleManageFEI();
    }
}




handleManageFEI(){
     const dt = this.quote.domainType?.trim().toUpperCase();
      if (dt === 'PU') {
        console.log('handleVisualizzaClick: APRI PORTALE UNICA');
        this.handleVisualizzaClick();
    } 
    // Altrimenti lancio il flow FEI
    else {
        console.log('handleVisualizzaClickPDF: casi ESSIG');
        this.handleVisualizzaClickPDF();
    }
}

    openPDFPreview()
    {
        this.showPreview = true;
    }

    closePDFPreview()
    {
        this.showPreview = false;
    }
}