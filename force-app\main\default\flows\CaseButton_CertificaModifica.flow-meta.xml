<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <constants>
        <name>FEI_AUTO</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RA.VARIAZIONE.POLIZZA</stringValue>
        </value>
    </constants>
    <constants>
        <name>FEI_RAMI</name>
        <dataType>String</dataType>
        <value>
            <stringValue>RE.VARIAZIONE.POLIZZA</stringValue>
        </value>
    </constants>
    <description>UNF-802</description>
    <environments>Default</environments>
    <interviewLabel>CaseButton_CertificaModifica {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CaseButton_CertificaModifica</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>callFei</targetReference>
        </connector>
    </start>
    <status>Draft</status>
    <subflows>
        <name>callFei</name>
        <label>callFei</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <flowName>FEIQuickAction</flowName>
        <inputAssignments>
            <name>FEIID</name>
            <value>
                <elementReference>FEI_AUTO</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>society</name>
        </inputAssignments>
    </subflows>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
