global with sharing class DRTRenewalsApex implements System.Callable {

    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');

        Object result = invokeMethod(action, input, output, options);
        return result;
    }

    global Boolean invokeMethod(String methodName, Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        Boolean result = true;

        try {
            if (methodName == 'getStartDates') {
                getStartDates(methodName, inputMap, outMap, options);
            }
        } catch (Exception e) {
            outMap.put('success', false);
            outMap.put('errorType', 0);
            outMap.put('message', 'Errore generico: ' + e.getMessage());
            result = false;
        }

        return result;
    }

private void getStartDates(String methodName, Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
    Date inputDate;

    if (inputMap != null && inputMap.containsKey('inputDate') && inputMap.get('inputDate') != null) {
        inputDate = Date.valueOf((String) inputMap.get('inputDate'));
    } else {
        inputDate = Date.today();
    }

    // Calcolo date
    Integer dayOfWeek = inputDate.toStartOfWeek().daysBetween(inputDate);
    Date startOfWeek = inputDate.addDays(-dayOfWeek);
    Date startOfMonth = Date.newInstance(inputDate.year(), inputDate.month(), 1);

    Map<String, Object> response = new Map<String, Object>();
    response.put('startWeek', formatDates(startOfWeek));
    response.put('startMonth', formatDates(startOfMonth));
    // response.put('startWeek', startOfWeek.format('dd-MM-yyyy'));
    // response.put('startMonth', startOfMonth.format('dd-MM-yyyy'));

    // Ottenere FiscalCode__c da User
    String userId = UserInfo.getUserId();
    String fiscalCode;
    try {
        User usr = [
            SELECT FiscalCode__c
            FROM User
            WHERE Id = :userId
            LIMIT 1
        ];
        fiscalCode = usr.FiscalCode__c;
        response.put('fiscalCodeUser', fiscalCode);
    } catch (Exception e) {
        outMap.put('response', response);
        return;
    }

    // Query NetworkUser__c tramite FiscalCode__c
    List<NetworkUser__c> networkUsers = [
        SELECT Id, Agency__c, FiscalCode__c, Society__c
        FROM NetworkUser__c
        WHERE FiscalCode__c = :fiscalCode
    ];

    Set<Id> agencyIds = new Set<Id>();
    for (NetworkUser__c nu : networkUsers) {
        if (nu.Agency__c != null) {
            agencyIds.add(nu.Agency__c);
        }
    }

    // Query con FinServ__ExternalId__c incluso
    List<FinServ__AccountAccountRelation__c> relations = [
        SELECT RecordType.DeveloperName, FinServ__Account__c, Identifier__c, FinServ__ExternalId__c
        FROM FinServ__AccountAccountRelation__c
        WHERE FinServ__Account__c IN :agencyIds
        AND RecordType.DeveloperName = 'AgencySociety'
    ];

Set<String> identifiers = new Set<String>();
for (FinServ__AccountAccountRelation__c rel : relations) {
    if (rel.Identifier__c != null && rel.FinServ__ExternalId__c != null) {
        // Estrarre numero finale da ExternalId
        String extId = rel.FinServ__ExternalId__c;
        Integer socIndex = extId.lastIndexOf('SOC_');
        if (socIndex != -1 && extId.length() > socIndex + 4) {
            String prefix = extId.substring(socIndex + 4); 
            identifiers.add(prefix + rel.Identifier__c);
        }
    }
}

    response.put('agency', new List<String>(identifiers));
    outMap.put('response', response);
}

    private static String formatDates(Date t) {
        Integer d = t.day();
        Integer m = t.month();
        Integer y = t.year();
        String dd = (d < 10 ? '0' + d : '' + d);
        String mm = (m < 10 ? '0' + m : '' + m);
        return dd + '-' + mm + '-' + y;
    }
}