import { LightningElement, wire, track, api} from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';
import { CurrentPageReference } from 'lightning/navigation';

import apexGetTitoliInScadenzaFromIntegrationProcedure from '@salesforce/apex/TitoliFeiPermissionHelper.getTitoliInScadenzaFromIntegrationProcedure';
import apexGetUserMandati from '@salesforce/apex/TitoliFeiPermissionHelper.getUserMandati';
import apexGetParamsForFei from '@salesforce/apex/TitoliFeiPermissionHelper.getParamsForFei';
import apexEnableActionsAsNeeded from '@salesforce/apex/TitoliFeiPermissionHelper.enableActionsAsNeeded';
import apexGetPrt from '@salesforce/apex/TitoliFeiPermissionHelper.getPrt';
import apexGetAccountData from '@salesforce/apex/TitoliFeiPermissionHelper.getAccountData';
//import apexGetAccountDetails from '@salesforce/apex/TitoliFeiPermissionHelper.getAccountDetails';
//import apexCreateBarcodeAndXML from '@salesforce/apex/TitoliFeiPermissionHelper.createBarcodeAndXML';


const actionsProdottoUnico = [
    { label: 'Completa', name: 'completa', disabled: false }, 
    { label: 'Incassa', name: 'incassa', disabled: false },
    { label: 'FEA', name: 'fea', disabled: true },
    { label: 'Firma', name: 'firma', disabled: true },
    { label: 'Rettifica', name: 'rettifica', disabled: true },
    //{ label: 'Vedi Documento', name: 'vediDocumento', disabled: true },
    //{ label: 'Incasso multiplo', name: 'incassoMultiplo', disabled: true },
    //{ label: 'Incasso', name: 'Incasso', disabled: true },
    //{ label: 'Invio da remoto', name: 'invioDaremoto', disabled: true },
    { label: 'Stampa', name: 'stampa', disabled: false },
    //{ label: 'Sostituzione', name: 'sostituzione', disabled: true },
    //{ label: 'Variazione', name: 'variazione', disabled: true },
    { label: 'Scheda QT', name: 'schedaQT', disabled: false }
    //{ label: 'Aggiungi ai preferiti', name: 'aggiungiAiPreferiti', disabled: true }
];

const actionsVecchiPortafogli = [
    { label: 'Incasso', name: 'incasso', disabled: false },
    { label: 'Incasso multiplo', name: 'incassoMultiplo', disabled: false },
    { label: 'Riprendi', name: 'riprendi', disabled: true },
    { label: 'Rettifica', name: 'rettifica', disabled: false },
    { label: 'Vedi Documento', name: 'vediDocumento', disabled: false },
    { label: 'Sostituzione', name: 'sostituzione', disabled: false },
    { label: 'Variazione', name: 'variazione', disabled: false },
    { label: 'Scheda QT', name: 'schedaQT', disabled: false },
    //{ label: 'Invio da Remoto', name: 'invioDaRemoto', disabled: false },
];

export default class TitoliInScadenzaCmp extends OmniscriptBaseMixin(LightningElement) {

    actionsProdottoUnico = actionsProdottoUnico;
    actionsVecchiPortafogli = actionsVecchiPortafogli;
    titoliInScadenzaColumn = [];
    titoliInScadenzaData = [];
    datatableRendered = false;
    isFlowModalOpened = false;
    isLWCModalOpened = false;

    labelLoadingTitoli = 'Caricamento titoli';
    showSpinner = false;

    params = {
        feiId: "feiId",
        fiscalcode: "CF",
        feiRequestPayload: "{}",
        permissionSetName: "permissionSetName"
    }

    @wire(CurrentPageReference)
    currentPageReference;

    @api accountId;
    accountData;

    connectedCallback() {

        if(this.currentPageReference?.state.c__AccountId != undefined){
            this.accountId = this.currentPageReference.state.c__AccountId;
        }

        this.getTitoliInScadenzaData();
        this.getUserMandati();
    }

    getTitoliInScadenzaData(){
        this.showSpinner = true;
        apexGetTitoliInScadenzaFromIntegrationProcedure({accountId : this.accountId})
        .then((result) => {
            this.titoliInScadenzaData = Array.isArray(result['Response']) ? result['Response'] : [result['Response']];
            //this.titoliInScadenzaData = JSON.parse('[{"isFEAAttiva":false,"isProdottoUnico":true,"isFirmaAttiva":false,"tipo":"Quietanza","omnicanalita":"D","isCompletaAttiva":false,"premio":232.08,"titoliAlLegale":"SI","modello":"LIBERTY 50","isFEAVisualizzata":true,"contId":"182556357703657402","esposizioneAR":"2024-12-29","xquietanzaId":"*********","ambito":"RCA","inviiRemoti":"0","numeroAppendice":"1","agenzia":"54776","dataEffettoTitolo":"2025-01-01T23:00:00.000Z","polizza":"*********","ramo":"030","prodottoAbilitatoInvioFea":true,"idDoc":"*********","targa":"X8GN99","isRettificaAttiva":false,"dataScadenzaTitolo":"01-01-2025","operazione":"I","inviiAr":"0","tipoDocumento":1000,"emessaNonIncassabile":false,"rettificabile":"NO","compagnia":"Unipol","isIncassaAttiva":false,"isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true},{"isFEAAttiva":false,"isProdottoUnico":false,"isFirmaAttiva":false,"tipo":"Quietanza","omnicanalita":"D","isCompletaAttiva":false,"premio":116,"titoliAlLegale":"NO","modello":"SH 125","isFEAVisualizzata":true,"contId":"182556357703657402","esposizioneAR":"2025-02-08","xquietanzaId":"231133090","ambito":"RCA","inviiRemoti":"1","uplCrmQuietId":"218601876","numeroAppendice":"0","agenzia":"54776","dataEffettoTitolo":"2025-04-13T22:00:00.000Z","polizza":"200141999","ramo":"030","prodottoAbilitatoInvioFea":true,"idDoc":"105959940","targa":"EF 48488","isRettificaAttiva":false,"dataScadenzaTitolo":"13-04-2025","operazione":"I","inviiAr":"0","tipoDocumento":1000,"emessaNonIncassabile":false,"rettificabile":"NO","compagnia":"Unipol","isIncassaAttiva":false,"unibox":"0","isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true},{"isFEAAttiva":false,"isProdottoUnico":false,"isFirmaAttiva":false,"tipo":"Quietanza","omnicanalita":"D","isCompletaAttiva":false,"premio":40.44,"titoliAlLegale":"NO","isFEAVisualizzata":true,"contId":"182556357703657402","esposizioneAR":"2025-02-08","xquietanzaId":"230236754","ambito":"REL","inviiRemoti":"1","uplCrmQuietId":"218578466","numeroAppendice":"1","agenzia":"54776","dataEffettoTitolo":"2025-04-27T22:00:00.000Z","polizza":"187389700","ramo":"077","prodottoAbilitatoInvioFea":true,"idDoc":"105091215","isRettificaAttiva":false,"dataScadenzaTitolo":"27-04-2025","operazione":"I","inviiAr":"0","tipoDocumento":1000,"emessaNonIncassabile":false,"rettificabile":"NO","compagnia":"Unipol","isIncassaAttiva":false,"isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true},{"isFEAAttiva":false,"isProdottoUnico":false,"isFirmaAttiva":false,"tipo":"Quietanza","omnicanalita":"D","isCompletaAttiva":false,"premio":40.44,"titoliAlLegale":"NO","isFEAVisualizzata":true,"contId":"182556357703657402","esposizioneAR":"2025-02-08","xquietanzaId":"230236754","ambito":"REL","inviiRemoti":"1","uplCrmQuietId":"218578466","numeroAppendice":"1","agenzia":"54776","dataEffettoTitolo":"2025-03-27T23:00:00.000Z","polizza":"187389700","ramo":"077","prodottoAbilitatoInvioFea":true,"idDoc":"105091215","isRettificaAttiva":false,"dataScadenzaTitolo":"27-03-2025","operazione":"I","inviiAr":"0","tipoDocumento":1000,"emessaNonIncassabile":false,"rettificabile":"NO","compagnia":"Unipol","isIncassaAttiva":false,"isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true}]');
            console.log('titoliInScadenzaDataResult: '+JSON.stringify(this.titoliInScadenzaData));
            
            this.enableActionsAsNeeded();
            this.showSpinner = false;
        })
        .catch((error) => {
            console.log('err getTitoli: ' + JSON.stringify(error));
            this.showSpinner = false;
        });
    }

    getUserMandati(){
        apexGetUserMandati({accId: this.accountId})
        .then((result) => {
            this.showUnipolButton = result['hasMandatoUnipol']; //TODO: aggiungi custom permission
            this.showUniSaluteButton = result['hasMandatoUnisalute'];
        })
        .catch((error) => {
            //TODO: insert show toast
            console.log(JSON.stringify(error));
        });
    }

    getRowActions(row, doneCallback) {
        const matchingRow = this.titoliInScadenzaData.find(r => r.rowId === row.rowId);
        const actions = matchingRow?.rowActions || [];
        doneCallback(actions);
    }

    enableActionsAsNeeded(){
        this.datatableRendered = false;
        this.showSpinner = true;
        apexEnableActionsAsNeeded({actionsJSON: JSON.stringify(this.actionsProdottoUnico)}) //se serve aggiungiamo anche vecchi portafogli
        .then((result) => {
            let arrayTitoli = [];
            let rowsProdottoUnico = this.titoliInScadenzaData.filter(prodottoUnicoRow => prodottoUnicoRow.isProdottoUnico === true);
            rowsProdottoUnico.forEach(titolo => {

                let feaPresent = true;
                let feaAction = {};
                let firmaAction = {};
                let currentRow = { ...titolo };

                currentRow.rowId = `${currentRow.nFolder || ''}_${currentRow.agenzia || ''}_${Math.random().toString(36).substring(2, 8)}`;
                if(currentRow.polizza != null && currentRow.polizza != undefined){
                    currentRow.polizza = currentRow.polizza.length > 1 ? currentRow.polizza.toString().replaceAll(',', '-') : currentRow.polizza.toString();
                    currentRow.ramoPolizza = currentRow.ramo != undefined ? currentRow.ramo + '/' + currentRow.polizza : currentRow.polizza;
                }
                if (currentRow.esposizioneAR != null && currentRow.esposizioneAR != undefined) {
                    let esposizioneArray = currentRow.esposizioneAR.split('-');
                    currentRow.esposizioneAR = esposizioneArray[2] + '-' +  esposizioneArray[1] + '-' +  esposizioneArray[0];
                }
                currentRow.rowActions = result.map(action => ({ ...action }));

                currentRow.rowActions.forEach(action => {

                    if (action.disabled === true && (action.name !== 'fea' || (action.name !== 'firma' && feaPresent === false))) {
                        return;
                    }

                    switch (action.name) {
                        case 'fea':
                            
                            if (currentRow?.tipo !== 'Quietanza') {
                                feaAction = action;
                                feaPresent = false;
                            } else {

                                if (currentRow?.isStampabileInFEA === false || currentRow?.prodottoAbilitatoInvioFea === false || currentRow?.inviiRemoti !== '0' || currentRow.inviiAr !== '0')  {
                                    action.disabled = true;
                                }
                            }
                                    
                            break;
                        
                        case 'completa':
                            if (currentRow?.isCompletaAttiva !== true) {
                                action.disabled = true;
                            }
                            /*
                            if (currentRow?.operazione === 'R' || (currentRow?.firmata === 'Firmato' && (currentRow?.emessaNonIncassabile === false || (currentRow?.frazionamento !== undefined && currentRow?.frazionamento.toLowerCase() !== 'annuale'))) || (currentRow?.idContrattoPTF === null || currentRow?.idContrattoPTF === undefined)){
                                action.disabled = true;
                            }
                            */
                            break;
                    
                        case 'incassa':
                            
                            if (currentRow?.isIncassaAttiva !== true) {
                                action.disabled = true;
                            }
                            /*
                            if(currentRow?.operazione === 'R' || currentRow?.firmata === 'Non firmato' || currentRow?.emessaNonIncassabile === true){
                                action.disabled = true;
                            }
                            */
                            break;

                        case 'rettifica':

                            if (currentRow?.isRettificaAttiva !== true) {
                                action.disabled = true;
                            }
                            /*
                            if (currentRow?.operazione === 'I') {
                                action.disabled = true;
                            }
                            */
                            break;
                        
                        case 'firma':

                            if (feaPresent === true) {
                                firmaAction = action;
                            } else {
                                if (currentRow?.isFirmaAttiva !== true) {
                                    action.disabled = true;
                                }
                                /*
                                if (currentRow?.firmata === 'Firmato' || (currentRow?.idContrattoPTF === null || currentRow?.idContrattoPTF === undefined)) {
                                    action.disabled = true;
                                }
                                */
                            }
                            break;

                        case 'schedaQT':
                                
                            if ((currentRow?.uplCrmQuietId === null || currentRow?.uplCrmQuietId  === undefined) || (currentRow?.contId === null || currentRow.contId === undefined)) {
                                action.disabled = true;
                            }
                            break;

                        default:
                            break;
                    }
                    
                });

                if (feaPresent == false) {
                    currentRow.rowActions.splice(currentRow.rowActions.indexOf(feaAction), 1);
                } else if (feaPresent == true) {
                    currentRow.rowActions.splice(currentRow.rowActions.indexOf(firmaAction), 1);
                }

                arrayTitoli.push(currentRow);
            });

            let rowsVecchiPortafogli = this.titoliInScadenzaData.filter(prodottoUnicoRow => prodottoUnicoRow.isProdottoUnico !== true);
            this.titoliInScadenzaData = [];
            rowsVecchiPortafogli.forEach(titolo => {

                let currentRow = { ...titolo };

                currentRow.rowId = `${currentRow.nFolder || ''}_${currentRow.agenzia || ''}_${Math.random().toString(36).substring(2, 8)}`;
                if(currentRow.polizza != null && currentRow.polizza != undefined){
                    currentRow.polizza = currentRow.polizza.length > 1 ? currentRow.polizza.toString().replaceAll(',', '-') : currentRow.polizza.toString();
                    currentRow.ramoPolizza = currentRow.ramo != undefined ? currentRow.ramo + '/' + currentRow.polizza : currentRow.polizza;
                }
                if (currentRow.esposizioneAR != null && currentRow.esposizioneAR != undefined) {
                    let esposizioneArray = currentRow.esposizioneAR.split('-');
                    currentRow.esposizioneAR = esposizioneArray[2] + '-' +  esposizioneArray[1] + '-' +  esposizioneArray[0];
                }
                console.log('actions vecchiP: ' + JSON.stringify(this.actionsVecchiPortafogli));
                currentRow.rowActions = this.actionsVecchiPortafogli.map(action => ({ ...action }));
                console.log('row actions vecchi: ' + JSON.stringify(currentRow.rowActions));
                currentRow.rowActions.forEach(action => {

                    if (action.disabled === true && (action.name !== 'fea' || (action.name !== 'firma' && feaPresent === false))) {
                        return;
                    }

                    switch (action.name) {
                        case 'incasso':

                            if (currentRow?.isIncassoAttiva !== true) {
                                action.disabled = true;
                            }
                            break;
                        
                        case 'incassoMultiplo':

                            if (currentRow?.isIncassoAttiva !== true) {
                                action.disabled = true;
                            }
                            break;
                        
                        case 'rettifica':
                            if (currentRow?.isRettificaAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;
                        
                        case 'schedaQT':
                                
                            if ((currentRow?.uplCrmQuietId === null || currentRow?.uplCrmQuietId  === undefined) || (currentRow?.contId === null || currentRow.contId === undefined)) {
                                action.disabled = true;
                            }
                            break;
                        
                        case 'vediDocumento':
                                
                            if (currentRow?.isVediDocumentoAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;

                        case 'sostituzione':
                                
                            if (currentRow?.isSostituzioneAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;
                        
                        case 'variazione':
                                
                            if (currentRow?.isVariazioneAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;

                        default:
                            break;
                    }
                    
                });

                arrayTitoli.push(currentRow);
            });

            console.log('titoliInScadenzaData post lavorazione: ' + JSON.stringify(arrayTitoli));
            this.titoliInScadenzaData = [...arrayTitoli];
            this.titoliInScadenzaColumn = [
                { label: 'Società', fieldName: 'compagnia' },
                { label: 'Tipo', fieldName: 'tipo' },
                { label: 'Ambito', fieldName: 'ambito' },
                { label: 'N. Folder', fieldName: 'nFolder' },
                { label: 'N. Polizza/Posizione', fieldName: 'ramoPolizza' },
                { label: 'Scadenza', fieldName: 'dataScadenzaTitolo' },
                { label: 'Frazionamento', fieldName: 'frazionamento' },
                { label: 'Premio (comprensivo di Unibox)', fieldName: 'premio', type: 'currency' },
                { label: 'di cui Unibox', fieldName: 'unibox', type: 'currency' },
                { label: 'Omnicanalità', fieldName: 'omnicanalita' },
                { label: 'Esposizione AR', fieldName: 'esposizioneAR', cellAttributes: { alignment: 'center' }},
                { label: 'Targa', fieldName: 'targa' },
                { label: 'Modello', fieldName: 'modello' },
                { label: 'Titoli al legale', fieldName: 'titoliAlLegale' },
                { label: 'Rettificabile', fieldName: 'rettificabile' },
                { label: 'Firmato', fieldName: 'firmata' },
                { type: 'action', typeAttributes: { rowActions: this.getRowActions.bind(this), menuAlignment: 'auto'} }
            ];
            this.datatableRendered = true;
            this.showSpinner = false;
        })
        .catch((error) => {
            console.log('err in ROWS: ' + JSON.stringify(error));
            this.showSpinner = false;
        });
    }

    handleStampe(event){
        const feiId = event.currentTarget.dataset.feiId;
        const society = event.currentTarget.dataset.societyId;
        this.flowInputs = [
            { name: 'FEIID', type: 'String', value: feiId},
            { name: 'recordId', type: 'String', value: this.accountId },
            { name: 'society', type: 'String', value: society }         
        ];
        this.toggleFlowModal();
    }

    toggleFlowModal(){
        this.isFlowModalOpened = !this.isFlowModalOpened;
    }

    toggleLWCModal(){
        this.isLWCModalOpened = !this.isLWCModalOpened;
    }

    handleFlowStatusChange(event) {
        if (event.detail.status === 'FINISHED' || event.detail.status === 'ERROR')
           this.toggleFlowModal();
    }

    handleRowAction(event){
        const actionName = event.detail.action.name;
        const row = event.detail.row;
        console.log('dataEffettoTitolo: ' + row.dataEffettoTitolo);
        console.log('polizza: ' + row.polizza);
        console.log("actionName: " + actionName);
        switch (actionName) {
            case 'completa': {
                let feiId = 'PU.COMPLETA';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({contractid: row.idContrattoPTF}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || ""
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'incassa': {
                let feiId = 'PU.INCASSO';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({agencyCode: '$agenzia', companyCode: '$compagnia', folderId: row.nFolder}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || ""
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'firma': {
                let feiId = 'PU.FIRMA';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({contractid: row.idContrattoPTF}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || ""
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'rettifica': {

                let feiId = row.isProdottoUnico == true ? 'PU.RETTIFICA' : 'CP.INCASSO';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {

                    if (feiId === 'PU.RETTIFICA') {
                        
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({agencyCode: '$agenzia', companyCode: '$compagnia', folderId: row.nFolder}),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || ""
                        };

                    } else if(feiId === 'CP.INCASSO'){
                        let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({operazione: 'R', w_Codage: row.agenzia, w_Compag: compagniaCode, w_Data: row.dataEffettoTitolo, w_Numpol: row.polizza, w_Ramopo: row.ramo}),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || ""
                        };
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'schedaQT': {
                let feiId = 'SCHEDA.QT';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({xqt: row.uplCrmQuietId, contid: row.contId, agency: row.agenzia}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || ""
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'stampa': {
                let stampaParams = this.getDefaultFEAParams(row);
                console.log('row selected: ' +JSON.stringify(row));
                stampaParams.prtForPrint = true;
                let uuid = self.crypto.randomUUID();
                console.log('uuid: '+uuid+'\n');
                let tipoDocumento = row.tipo != undefined && row.tipo == 'Quietanza' ? "Q" : row.tipo != undefined && row.tipo == "Regolazione Premio" ? "QTREG" : "";
                stampaParams.body = JSON.stringify({
                    "filtroStampaQtzListaDocumentiDaStampare": {
                        "ristampa": true,
                        "fromFe": true,
                        "motRistampaTpCd": "-1",
                        "testoMotRistampa": "Stampa precedente rovinata",
                        "flagAnteprima": false,
                        "flagGeneraDocumenti": false,
                        "listaDocumentiDaStampare": [
                            {
                                "idQt": row?.uplCrmQuietId,
                                "tipoDocumento": tipoDocumento,
                                "idDocumento": row?.idDoc
                            }
                        ],
                        "docTpCd": row?.tipoDocumento,
                        "guid": uuid,
                        "agenziaFiglia": row?.agenzia,
                        "agenziaMadre": row?.agenziaMadre,
                        "compagnia": row?.compagnia
                    }
                });
                console.log(`stampaParams: ${JSON.stringify(stampaParams)}`);
                apexGetPrt({inputParams: stampaParams})
                .then(resultPrt => {

                    //continua qui
                    const feiId = 'SFEA.PRINT.PAPER';
                    apexGetParamsForFei({feiId: feiId})
                    .then(result => {
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify(resultPrt), //TODO: metti json completo
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || ""
                        };
                        console.log("params: "+JSON.stringify(this.params));
                        this.toggleLWCModal();
                    });
                }).catch(error => {
                        console.error('Error retrieving PRT:', error);
                })

                break;
            }
            case 'fea': {
                let feaParams = this.getDefaultFEAParams(row);
                feaParams.prtForPrint = false;

                /*
                apexGetAccountDetails({accountId: this.accountId, societa: row.societa})
                .then(result => {
                    let accountDetailsMap = {};
                    if (result.length > 0) {
                        
                        let accountDetails = result[0];
                        accountDetailsMap.ciu = accountDetails.SourceSystemIdentifier__c;
                        accountDetailsMap.mobile = accountDetails.Mobile__c;
                        accountDetailsMap.email = accountDetails.Email__c;
                        accountDetailsMap.type = 'FEA' ;
                    }

                    apexCreateBarcodeAndXML({inputMap: accountDetailsMap})
                    .then(result => {
                        console.log('barcode And XML:', JSON.stringify(result));
                        //continua qui
                        let jsonOutput = result['feiRequest'];
                    */
                        apexGetPrt({inputParams: feaParams}).then(result => {
                            console.log('PRT retrieved successfully:', result);
                            this.executeFeaFei(result);
                        }).catch(error => {
                                console.error('Error retrieving PRT:', error);
                        })
                    /*
                    })
                

                })
                    */
                break;
            }
            case 'incasso': {

                let feiId = 'CP.INCASSO';
                console.log('dataEffettoTitolo: ' + row.dataEffettoTitolo);
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({operazione: 'I', w_Codage: row.agenzia, w_Compag: compagniaCode, w_Data: row.dataEffettoTitolo, w_Numpol: row.polizza, w_Ramopo: row.ramo}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || ""
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                })

                break;
            }
            case 'incassoMultiplo': {

                let feiId = 'CP.INCASSO.MULTIPLO';
                console.log('dataEffettoTitolo: ' + row.dataEffettoTitolo);
                apexGetAccountData({accountId: this.accountId})
                .then(resultAcc => {
                    this.accountData = resultAcc;
                    console.log('accountData:  ' + JSON.stringify(resultAcc));
                    apexGetParamsForFei({feiId: feiId})
                    .then(result => {
                        let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({operazione: 'I', w_Codage: row.agenzia, w_Compag: compagniaCode, w_Data: row.dataEffettoTitolo, w_Codcl: this.accountData.ExternalId__c }),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || ""
                        }
                        console.log("params: "+JSON.stringify(this.params));
                        this.toggleLWCModal();
                    })
                })
                break;
            }
            case 'sostituzione': {
                let feiId = 'NPAC.SOSTITUZIONE.POLIZZA';
                apexGetAccountData({accountId: this.accountId})
                .then(resultAcc => {
                    this.accountData = resultAcc;
                    console.log('accountData:  ' + JSON.stringify(resultAcc));
                    apexGetParamsForFei({feiId: feiId})
                    .then(result => {
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({
                                agenzia: row.agenzia, 
                                agenziaSostituita: row.agenzia, //da confermare
                                codiceCampagna: "1", //da confermare
                                codiceFiscale: this.accountData.ExternalId__c,
                                compagnia: row.societa,
                                compagniaSostituita: "1", //da confermare,
                                polizzaSostituita: row.polizza,
                                ramoSostituita: row.ramo,
                                subAgenzia: "101",
                                tipoOperazione: "SO" //statico
                            }),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || ""
                        }
                        console.log("params: "+JSON.stringify(this.params));
                        this.toggleLWCModal();
                    })
                })
                break;
            }
            case 'variazione': {
                let feiId = 'RA.VARIAZIONE.POLIZZA';
                apexGetAccountData({accountId: this.accountId})
                .then(resultAcc => {
                    this.accountData = resultAcc;
                    console.log('accountData:  ' + JSON.stringify(resultAcc));
                    apexGetParamsForFei({feiId: feiId})
                    .then(result => {
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({
                                agenzia: row.agenzia, 
                                agenziaSostituita: row.agenzia, //da confermare
                                codiceFiscale: this.accountData.ExternalId__c,
                                compagnia: row.societa,
                                compagniaSostituita: "1", //da confermare,
                                polizzaSostituita: row.polizza,
                                ramoSostituita: row.ramo,
                                subAgenzia: "101",
                                tipoOperazione: "VA" //statico
                            }),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || ""
                        }
                        console.log("params: "+JSON.stringify(this.params));
                        this.toggleLWCModal();
                    })
                })
                break;
            }
            case 'vediDocumento': {
                let feiId = 'SFEA.PRINT.DRAFT';
                apexGetAccountData({accountId: this.accountId})
                .then(resultAcc => {
                    this.accountData = resultAcc;
                    console.log('accountData:  ' + JSON.stringify(resultAcc));
                    apexGetParamsForFei({feiId: feiId})
                    .then(result => {
                        let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({
                                feasRequest: {
                                    mnuAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page',
                                    cmp: row.societa,
                                    prj: 'leon', //da confermare
                                    app: 'SFCRM', //statico
                                    finalPrint: false, //da confermare
                                    saveStatus: false, //da confermare
                                    sendMail: false, //da confermare
                                    sendSms: false, //da confermare
                                    watermarkTyp: 'QUIET', //da confermare
                                    keys: {
                                        NARC: {
                                            value: '***********', //da confermare
                                            name: NARC
                                        },
                                        SUBAG: {
                                            value: '101', //da confermare
                                            name: SUBAG
                                        },
                                        CODFIS: {
                                            value: this.accountData.ExternalId__c,
                                            name: CODFIS
                                        },
                                        RAM: {
                                            value: row.ramo,
                                            name: RAM
                                        },
                                        NPOL: {
                                            value: row.polizza,
                                            name: NPOL
                                        },
                                        NTRG: {
                                            value: row.targa,
                                            name: NTRG
                                        },
                                        AGEN: {
                                            value: row.agenzia,
                                            name: AGEN
                                        },
                                        COMP: {
                                            value: row.societa,
                                            name: COMP
                                        },
                                        MSGID: {
                                            value: '***********', //PRT??
                                            name: MSGID
                                        },
                                        BTCID: {
                                            value: '***********', //PRT...
                                            name: STCID
                                        },
                                        DOCID: {
                                            value: row.idDoc, //da confermare
                                            name: DOCID
                                        },
                                        ITEMID: {
                                            value: '999967059434896365', //da confermare
                                            name: ITEMID
                                        },
                                    }
                                }
                            }),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || ""
                        }
                        console.log("params: "+JSON.stringify(this.params));
                        this.toggleLWCModal();
                    })
                })
                break;
            }
            case 'riprendi': {
                let feiId = 'SFEA.VIEW.DASHBOARD';
                apexGetAccountData({accountId: this.accountId})
                .then(resultAcc => {
                    this.accountData = resultAcc;
                    console.log('accountData:  ' + JSON.stringify(resultAcc));
                    apexGetParamsForFei({feiId: feiId})
                    .then(result => {
                        let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({
                                feasRequest: {
                                    mnuAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/scheda_cliente',
                                    srcAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page',
                                    srcAppFailBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/[rotta da definire]',
                                    keys: {
                                        AGEN: {
                                            value: row.agenzia,
                                            name: AGEN
                                        },
                                        COMP: {
                                            value: row.societa,
                                            name: COMP
                                        },
                                        RAM: {
                                            value: row.ramo,
                                            name: RAM
                                        },
                                        NPOL: {
                                            value: row.polizza,
                                            name: NPOL
                                        },
                                        TIPOP: {
                                            value: '01', //da confermare
                                            name: TIPOP
                                        }
                                    }
                                }
                            }),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || ""
                        }
                        console.log("params: "+JSON.stringify(this.params));
                        this.toggleLWCModal();
                    })
                })
                break;
            }

            default:
                console.warn('Azione non riconosciuta:', actionName);
                this.params = null;
        }
    }

    executeFeaFei(params, ){
        if (params.FEI == 'requestSign'){
            let feiId = 'SFEA.SIGN';
            apexGetParamsForFei({feiId: feiId})
            .then(result => {
                this.params = {
                    feiId: feiId,
                    feiRequestPayload: JSON.stringify(params), //TODO: metti json completo
                    fiscalcode: result["fiscalCode"] || "CF",
                    permissionSetName: result["permissionSetName"] || ""
                };
                console.log("params: "+JSON.stringify(this.params));
                this.toggleLWCModal();
            }).catch(error => {
                console.log('Error retrieving parameters for FEA:' + JSON.stringify(error));
                }
            );
            return;
        }
        if (params.FEI == 'resumeSign'){
            let feiId = 'SFEA.SIGN.DASHBOARD';
            apexGetParamsForFei({feiId: feiId})
            .then(result => {
                this.params = {
                    feiId: feiId,
                    feiRequestPayload: JSON.stringify(params), //TODO: metti json completo
                    fiscalcode: result["fiscalCode"] || "CF",
                    permissionSetName: result["permissionSetName"] || ""
                };
                console.log("params: "+JSON.stringify(this.params));
                this.toggleLWCModal();
            }).catch(error => {
                console.log('Error retrieving parameters for FEA:' + JSON.stringify(error));
                }
            );
        }
    }

    getDefaultFEAParams(row) {
        return {
            idDoc: row.idDoc,
            ramo: row.ramo,
            polizza: row.polizza,
            polizzaProdottoUnico: row.isProdottoUnico,
            numeroAppendice: row.numeroAppendice,
            dataEffettoTitolo: row.dataEffettoTitolo
        };

    }
}