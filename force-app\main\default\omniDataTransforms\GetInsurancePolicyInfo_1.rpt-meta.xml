<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;DME_GetInsurancePolicyKey&quot; : null,
    &quot;DME_GetCaseInfo&quot; : null,
    &quot;DME_GetPhone&quot; : null,
    &quot;SV_ResponseBasedOnConditions&quot; : null,
    &quot;ResponseAction1&quot; : null,
    &quot;IPA_DoCallout&quot; : null,
    &quot;SV_BodyFinalForCallout&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>GetInsurancePolicyInfo</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterDataType>ID</filterDataType>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>InsurancePolicyId</filterValue>
        <globalKey>GetInsurancePolicyInfoCustom3363</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePolicyInfo</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePolicyInfoCustom3615</globalKey>
        <inputFieldName>ReferencePolicyNumber</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePolicyInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePolicyInfoCustom5041</globalKey>
        <inputFieldName>SourceSystemIdentifier</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePolicyInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePolicyInfoCustom282</globalKey>
        <inputFieldName>Name</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePolicyInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePolicyInfoCustom8764</globalKey>
        <inputFieldName>PolicyName</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePolicyInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetInsurancePolicyInfoCustom7207</globalKey>
        <inputFieldName>UniversalPolicyNumber</inputFieldName>
        <inputObjectName>InsurancePolicy</inputObjectName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetInsurancePolicyInfo</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>insurancePolicy</outputFieldName>
        <outputObjectName>Turbo Extract</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;insurancePolicyId&quot; : &quot;0YT9O0000025A8sWAE&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Turbo Extract</type>
    <uniqueName>GetInsurancePolicyInfo_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
