global with sharing class DRTFormatDates implements System.Callable {

    // Entry point per OmniStudio Integration Procedure Remote Action
    global Object call(String action, Map<String, Object> args) {
        Map<String, Object> inputMap  = (Map<String, Object>) args.get('input');
        Map<String, Object> outMap    = (Map<String, Object>) args.get('output');
        Map<String, Object> options   = (Map<String, Object>) args.get('options');
        return invokeMethod(action, inputMap, outMap, options);
    }

    // Dispatcher
    global Boolean invokeMethod(
        String methodName,
        Map<String, Object> inputMap,
        Map<String, Object> outMap,
        Map<String, Object> options
    ) {
        Boolean result = true;
        try {
            if (methodName == 'formatDatesRange') {
                formatDatesRange(inputMap, outMap);
            } else {
                outMap.put('message', 'Metodo non riconosciuto: ' + methodName);
                result = false;
            }
        } catch (Exception e) {
            outMap.put('success', false);
            outMap.put('message', 'Errore: ' + e.getMessage());
            result = false;
        }
        return result;
    }

    /**
     * Converte due date in formato dd-MM-yyyy e le restituisce nell'output.
     * Parametri attesi in inputMap:
     *  - dataA  : Date
     *  - dataDa : Date
     */
    private void formatDatesRange(Map<String, Object> inputMap, Map<String, Object> outMap) {
        String dataA  = (inputMap.containsKey('dataA')  && inputMap.get('dataA')  != null) ? (String) inputMap.get('dataA')  : null;
        String dataDa = (inputMap.containsKey('dataDa') && inputMap.get('dataDa') != null) ? (String) inputMap.get('dataDa') : null;
        String formattedA  = (dataA != null)  ? convertToIso(dataA)  : null;
        String formattedDa = (dataDa != null) ? convertToIso(dataDa) : null;
        outMap.put('dataA',  formattedA);
        outMap.put('dataDa', formattedDa);
    }


    /**
     * Converte una stringa data dal formato dd-MM-yyyy al formato yyyy-MM-dd.
     */
    private static String convertToIso(String dateStr) {
        if (String.isBlank(dateStr)) {
            return dateStr;
        }
        // Se la stringa è già nel formato ISO yyyy-MM-dd la restituiamo invariata
        if (Pattern.matches('^\\d{4}-\\d{2}-\\d{2}$', dateStr)) {
            return dateStr;
        }
        List<String> parts = dateStr.split('-');
        if (parts.size() != 3) {
            return dateStr; 
        }
        return parts[2] + '-' + parts[1] + '-' + parts[0];
    }
}