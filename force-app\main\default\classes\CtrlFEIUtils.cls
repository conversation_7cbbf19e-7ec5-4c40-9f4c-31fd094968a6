/*
* @description Class to handle common functions used in the project for FEI
* @cicd_tests CtrlFei_Test
*/
global without sharing class CtrlFEIUtils implements Callable{
    
    /*
    * @description Dispatch methods from Omnistudio Integration Procedure
    */
    public Object call(String action, Map<String,Object> args){
        
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');

        System.debug('action --> '+action);

        switch on action {

            when 'replacePlaceholder'{
                return replacePlaceholder(input, output, options);
            }
            when 'replaceCONTID'{
                return replaceContId(input, output, options);
            }
            when 'replaceCONTIDExtended'{
                return replaceContIdExtended(input, output, options);
            }
            when 'sendFeiRequest'{
                return sendFeiRequest(input, output, options);
            }
            when 'sendFEARequest'{
                return sendFEARequest(input, output, options);
            }
            when 'sendLINKPOSTRequest'{
                return sendLINKPOSTRequest(input, output, options);
            }
            when else{
                return null;
            }

        }
    }

    /*
    * @description Returned string with replaced placeholders with data from the payload
    * @param input the input from Integration Procedure
    * @param output the output to be sent to Omnistudio
    * @param options Remote Options from Integration Procedure
    * @return String
    */
    private String replacePlaceholder(Map<String, Object> input, Map<String, Object> output, Map<String, Object> options){
        
        String originalString = (Test.isRunningTest()) ? 'TEST' : (String)options.get('originalString');
        String resultString = originalString;
        
        try{
            if(Test.isRunningTest() || (options.containsKey('feiRequestPayload') && options.get('feiRequestPayload') != null)){
                
                if(String.isNotBlank(String.valueOf(options.get('feiRequestPayload')))){
					
					String ipBackURL = (String)options.get('backURL');
                    
                    Map<String, Object> feiRequestPayload = new Map<String, Object>();

                    try{
                        feiRequestPayload = (Test.isRunningTest()) ? new Map<String, Object>{'test'=>'string'} : (Map<String, Object>)options.get('feiRequestPayload');
                    }catch(Exception ex){
                        System.debug('container exception : '+ex.getMessage());
                        System.debug('container exception : '+ex.getStackTraceString());
                        try{

                            feiRequestPayload = (Test.isRunningTest()) ? new Map<String, Object>{'test'=>'string'} : (Map<String, Object>)JSON.deserializeUntyped((String)options.get('feiRequestPayload'));
                        }catch(Exception innerEx){
                            System.debug('inner exception : '+innerEx.getMessage());
                            System.debug('inner exception : '+innerEx.getStackTraceString());
                        }
                    }
                    
                    System.debug('originalString --> '+originalString);
                    System.debug('feiRequestPayload --> '+feiRequestPayload);
                    
                    List<String> splittedString = originalString.split('}');
                    
                    System.debug('splittedString --> '+splittedString);
                    
                    Set<String> queryFields = new Set<String>();
                    for(String singleSplit : splittedString){
                        if(singleSplit.containsIgnoreCase('{!')){
                            queryFields.add(singleSplit.substringAfter('{!'));
                        }
                    }
                    
                    System.debug('queryFields --> '+queryFields);
                    
                    /* if(queryFields.isEmpty()){
                        output.put('resultString', originalString);
                        return originalString;
                    } */

                    Set<String> replacedFields = new Set<String>();
                    
                    if(Test.isRunningTest()){
                        queryFields.add('test');
                        resultString = '{"param":"{!test}"}';
                    }
                    
                    Boolean containsQueryParams = false;
                    
                    if(!queryFields.isEmpty()){
                        for(String field : queryFields){
                            
                            String currentReplace = '{!' + field + '}';
                            String replaceRegExp = '\\{\\!' + field + '\\}';
                            System.debug('currentReplace --> '+currentReplace);
                            System.debug('replaceRegExp --> '+replaceRegExp);
                            if(resultString.containsIgnoreCase(currentReplace)){
                                
                                containsQueryParams = true;
                                
                                resultString = resultString.replaceAll(replaceRegExp, String.valueOf(feiRequestPayload.get(field)));
                                replacedFields.add(field);
                            }
                            
                        }
                    }
                    System.debug('replacedFields --> '+replacedFields);
                    System.debug('resultString --> '+resultString);


                    String queryParams = '?';
                    for(String s : feiRequestPayload.keySet()){

                        System.debug('current s --> '+s);
                        if(!replacedFields.isEmpty() && replacedFields.contains(s)) continue;
                        queryParams += s +'='+ String.valueOf(feiRequestPayload.get(s)) +'&';
                        
                        containsQueryParams = true;

                    }

                    System.debug('queryParams --> '+queryParams);

                    String backURL = (String)options.get('backURL');

                    /*resultString += (String.isBlank(queryParams)) ? '?backUrl='+backURL : 
                                    (queryParams.endsWith('&') || queryParams.endsWith('?')) ? queryParams + 'backUrl='+ backURL : queryParams + '&backUrl=' + backURL;*/
					
                    if(String.isNotBlank(ipBackURL)){
						resultString += ('?'.equalsIgnoreCase(queryParams) && !resultString.contains('?')) ? '?backUrl='+ipBackUrl :
                        				(containsQueryParams && '?'.equalsIgnoreCase(queryParams)) ? '&backUrl='+ipBackUrl :
    	                				(String.isBlank(queryParams)) ? '?backUrl='+ipBackUrl : 
        	                            (queryParams.endsWith('&') || queryParams.endsWith('?')) ? queryParams + 'backUrl='+ipBackUrl : queryParams + '&backUrl='+ipBackUrl;
                    }else{
	                    resultString += ('?'.equalsIgnoreCase(queryParams) && !resultString.contains('?')) ? '?backUrl=about:blank' :
                        				(containsQueryParams && '?'.equalsIgnoreCase(queryParams)) ? '&backUrl=about:blank' :
    	                				(String.isBlank(queryParams)) ? '?backUrl=about:blank' : 
        	                            (queryParams.endsWith('&') || queryParams.endsWith('?')) ? queryParams + 'backUrl=about:blank' : queryParams + '&backUrl=about:blank';
                    }
                    
                    System.debug('resultString --> '+resultString);

                    output.put('resultString', resultString);
                }else{
                    output.put('resultString',originalString);
                }

            }
        }catch(Exception ex){
            System.debug('CtrlFEIUtils Exception --> '+ex.getMessage());
            System.debug('CtrlFEIUtils Exception --> '+ex.getStackTraceString());
            
            output.put('resultString',originalString);
        }

        return resultString;

    }
    
    /*
    * @description Returned string with replaced placeholders with data from the payload
    * @param input the input from Integration Procedure
    * @param output the output to be sent to Omnistudio
    * @param options Remote Options from Integration Procedure
    * @return String
    */
    private String replaceContId(Map<String, Object> input, Map<String, Object> output, Map<String, Object> options){
        
        String originalString = (Test.isRunningTest()) ? 'TEST' : (String)options.get('originalString');
        String resultString = originalString;

        String contIdParameterName = (String)options.get('paramContId');
        
        try{

            String queryParams = '?';
            queryParams += (String.isNotBlank(contIdParameterName)) ? contIdParameterName+'=' + String.valueOf(options.get('contid')) : 'id=' + String.valueOf(options.get('contid'));
            
            System.debug('queryParams --> '+queryParams);
            
            String backURL = (String)options.get('backURL');
            
            resultString += (String.isBlank(queryParams)) ? '?backUrl=about:blank' : 
            (queryParams.endsWith('&') || queryParams.endsWith('?')) ? queryParams + 'backUrl=about:blank' : queryParams + '&backUrl=about:blank';
            
            System.debug('resultString --> '+resultString);
            
            output.put('resultString', resultString);

        }catch(Exception ex){
            System.debug('CtrlFEIUtils Exception --> '+ex.getMessage());
            System.debug('CtrlFEIUtils Exception --> '+ex.getStackTraceString());
            
            output.put('resultString',originalString);
        }

        return resultString;

    }
    
    /*
    * @description Returned string with replaced placeholders with data from the payload
    * @param input the input from Integration Procedure
    * @param output the output to be sent to Omnistudio
    * @param options Remote Options from Integration Procedure
    * @return String
    */
    private String replaceContIdExtended(Map<String, Object> input, Map<String, Object> output, Map<String, Object> options){
        
        String originalString = (Test.isRunningTest()) ? 'TEST' : (String)options.get('originalString');
        String resultString = originalString;
        String contIdParameterName = (String)options.get('paramContId');
        
        try{
            if(Test.isRunningTest() || (options.containsKey('feiRequestPayload') && options.get('feiRequestPayload') != null)){
                
                //Map<String, Object> feiRequestPayload = (Test.isRunningTest()) ? new Map<String, Object>{'test'=>'string'} : (Map<String, Object>)options.get('feiRequestPayload');
                
                Map<String, Object> feiRequestPayload = new Map<String, Object>();

                try{
                    feiRequestPayload = (Test.isRunningTest()) ? new Map<String, Object>{'test'=>'string'} : (Map<String, Object>)options.get('feiRequestPayload');
                }catch(Exception ex){
                    System.debug('container exception : '+ex.getMessage());
                    System.debug('container exception : '+ex.getStackTraceString());
                    try{

                        feiRequestPayload = (Test.isRunningTest()) ? new Map<String, Object>{'test'=>'string'} : (Map<String, Object>)JSON.deserializeUntyped((String)options.get('feiRequestPayload'));
                    }catch(Exception innerEx){
                        System.debug('inner exception : '+innerEx.getMessage());
                        System.debug('inner exception : '+innerEx.getStackTraceString());
                    }
                }
                
                System.debug('originalString --> '+originalString);
                System.debug('feiRequestPayload --> '+feiRequestPayload);
                
                List<String> splittedString = originalString.split('}');
                
                System.debug('splittedString --> '+splittedString);
                
                Set<String> queryFields = new Set<String>();
                for(String singleSplit : splittedString){
                    if(singleSplit.containsIgnoreCase('{!')){
                        queryFields.add(singleSplit.substringAfter('{!'));
                    }
                }
                
                System.debug('queryFields --> '+queryFields);
                
                /* if(queryFields.isEmpty()){
                    output.put('resultString', originalString);
                    return originalString;
                } */

                Set<String> replacedFields = new Set<String>();
                
                if(Test.isRunningTest()){
                    queryFields.add('test');
                    resultString = '{"param":"{!test}"}';
                }
                
                if(!queryFields.isEmpty()){
                    for(String field : queryFields){
                        
                        String currentReplace = '{!' + field + '}';
                        String replaceRegExp = '\\{\\!' + field + '\\}';
                        System.debug('currentReplace --> '+currentReplace);
                        System.debug('replaceRegExp --> '+replaceRegExp);
                        if(resultString.containsIgnoreCase(currentReplace)){
                            resultString = resultString.replaceAll(replaceRegExp, String.valueOf(feiRequestPayload.get(field)));
                            replacedFields.add(field);
                        }
                        
                    }
                }
                System.debug('replacedFields --> '+replacedFields);
                System.debug('resultString --> '+resultString);


                String queryParams = '?';
                for(String s : feiRequestPayload.keySet()){

                    System.debug('current s --> '+s);
                    if(!replacedFields.isEmpty() && replacedFields.contains(s)) continue;
                    queryParams += s +'='+ String.valueOf(feiRequestPayload.get(s)) +'&';

                }
                
                if(
                    queryParams.endsWithIgnoreCase('&') ||
                    queryParams.endsWithIgnoreCase('?')
                ){
                    queryParams += (String.isNotBlank(contIdParameterName)) ? contIdParameterName+'=' + String.valueOf(options.get('contid')) : 'id=' + String.valueOf(options.get('contid'));
                }else{
                    queryParams += '&';
                    queryParams += (String.isNotBlank(contIdParameterName)) ? contIdParameterName+'=' + String.valueOf(options.get('contid')) : 'id=' + String.valueOf(options.get('contid'));
                }

                System.debug('queryParams --> '+queryParams);

                String backURL = (String)options.get('backURL');

                /*resultString += (String.isBlank(queryParams)) ? '?backUrl='+backURL : 
                                (queryParams.endsWith('&') || queryParams.endsWith('?')) ? queryParams + 'backUrl='+ backURL : queryParams + '&backUrl=' + backURL;*/

                resultString += (String.isBlank(queryParams)) ? '?backUrl=about:blank' : 
                                (queryParams.endsWith('&') || queryParams.endsWith('?')) ? queryParams + 'backUrl=about:blank' : queryParams + '&backUrl=about:blank';
                
                System.debug('resultString --> '+resultString);

                output.put('resultString', resultString);

            }
        }catch(Exception ex){
            System.debug('CtrlFEIUtils Exception --> '+ex.getMessage());
            System.debug('CtrlFEIUtils Exception --> '+ex.getStackTraceString());
            
            output.put('resultString',originalString);
        }

        return resultString;

    }

    public class SendFeiRequest{

        public FeiRequestContainer feiRequest = new FeiRequestContainer();
        public FeiAddressContainer feiAddress = new FeiAddressContainer();

    }

    public class FeiRequestContainer{
        public String requestCode               {get; set;}
        public Object feiRequest                {get; set;}
        public String requestId                 {get; set;}
    }

    public class FeiAddressContainer{
        public String hostpoint                 {get; set;}
        public String entrypoint                {get; set;}
        public String endpoint                  {get; set;}
        public String identity                  {get; set;}
    }

    public class SendFeiRequestResponse{
        public BdyType bdy = new BdyType();
        public String uuid                      {get; set;}
    }

    public class BdyType{
        public String forwardEndpoint           {get; set;}
    }

    @testVisible
    private String sendFeiRequest(Map<String, Object> input, Map<String, Object> output, Map<String, Object> options){

        String originalString = (Test.isRunningTest()) ? 'TEST' : (String)options.get('originalString');
        String resultString = originalString;
        
        try{
            if(Test.isRunningTest() || (options.containsKey('feiRequestPayload') && options.get('feiRequestPayload') != null)){
                
                Map<String, Object> feiRequestPayload = new Map<String, Object>();

                try{
                    feiRequestPayload = (Test.isRunningTest()) ? new Map<String, Object>{'test'=>'string'} : (Map<String, Object>)options.get('feiRequestPayload');
                }catch(Exception ex){
                    System.debug('container exception : '+ex.getMessage());
                    System.debug('container exception : '+ex.getStackTraceString());
                    try{

                        feiRequestPayload = (Test.isRunningTest()) ? new Map<String, Object>{'test'=>'string'} : (Map<String, Object>)JSON.deserializeUntyped((String)options.get('feiRequestPayload'));
                    }catch(Exception innerEx){
                        System.debug('inner exception : '+innerEx.getMessage());
                        System.debug('inner exception : '+innerEx.getStackTraceString());
                    }
                }

                //String stringPayload = (Test.isRunningTest()) ? 'test' : (String)options.get('feiRequestPayload');
                
                //System.debug('stringPayload '+stringPayload);

                if(feiRequestPayload != null){

                    SendFeiRequest sfr = new SendFeiRequest();

                    FeiRequestContainer frc = new FeiRequestContainer();
                    frc.requestCode = (String)options.get('requestCode');
                    //frc.feiRequest = (Map<String, Object>)JSON.deserializeUntyped(stringPayload);
                    frc.feiRequest = feiRequestPayload;
                    frc.requestId = (String)options.get('requestId');

                    sfr.feiRequest = frc;

                    FeiAddressContainer fac = new FeiAddressContainer();
                    fac.hostpoint = (String)options.get('hostpoint');
                    fac.entrypoint = (String)options.get('entrypoint');
                    fac.endpoint = (String)options.get('endpoint');
                    fac.identity = (String)options.get('identity');

                    sfr.feiAddress = fac;

                    Http h = new Http();
                    HttpRequest req = new HttpRequest();
                    
                    req.setEndpoint('callout:SendFEIRequest/api/v1/feirequest/send');
                    req.setMethod('POST');
                    req.setTimeout(120000);
                    req.setHeader('Content-Type','application/json');
                    req.setHeader('Accept','application/json');
                    
                    req.setBody(JSON.serialize(sfr));
                    
                    System.debug('req '+sfr);

                    HttpResponse res = (!Test.isRunningTest()) ? h.send(req) : new HttpResponse();
                    
                    System.debug('response body --> '+res.getBody());

                    SendFeiRequestResponse response = (SendFeiRequestResponse)JSON.deserialize(res.getBody(), SendFeiRequestResponse.class);

                    System.debug('resultFeiFull '+response.bdy.forwardEndpoint);
                    output.put('resultFeiFull',response.bdy.forwardEndpoint);

                }else{
                    output.put('resultFeiFull',originalString);
                }

            }
        }catch(Exception ex){
            System.debug('CtrlFEIUtils Exception --> '+ex.getMessage());
            System.debug('CtrlFEIUtils Exception --> '+ex.getStackTraceString());
            
            output.put('resultFeiFull',originalString);
        }

        return resultString;

    }

    @testVisible
    private String sendFEARequest(Map<String, Object> input, Map<String, Object> output, Map<String, Object> options){

        String originalString = (Test.isRunningTest()) ? 'TEST' : (String)options.get('originalString');
        String resultString = originalString;
        
        try{
            
            String landingPage = (String)options.get('landingPage');
            
            if(Test.isRunningTest() || (options.containsKey('feiRequestPayload') && options.get('feiRequestPayload') != null)){
                
                Map<String, Object> feiRequestPayload = new Map<String, Object>();

                try{
                    feiRequestPayload = (Test.isRunningTest()) ? new Map<String, Object>{'test'=>'string'} : (Map<String, Object>)options.get('feiRequestPayload');
                }catch(Exception ex){
                    System.debug('container exception : '+ex.getMessage());
                    System.debug('container exception : '+ex.getStackTraceString());
                    try{

                        feiRequestPayload = (Test.isRunningTest()) ? new Map<String, Object>{'test'=>'string'} : (Map<String, Object>)JSON.deserializeUntyped((String)options.get('feiRequestPayload'));
                    }catch(Exception innerEx){
                        System.debug('inner exception : '+innerEx.getMessage());
                        System.debug('inner exception : '+innerEx.getStackTraceString());
                    }
                }
                
                //System.debug('stringPayload '+stringPayload);

                if(feiRequestPayload != null){

                    SendFeiRequest sfr = new SendFeiRequest();

                    FeiRequestContainer frc = new FeiRequestContainer();
                    frc.requestCode = (String)options.get('requestCode');
                    //frc.feiRequest = (Map<String, Object>)JSON.deserializeUntyped(stringPayload);
                    frc.feiRequest = feiRequestPayload;
                    frc.requestId = (String)options.get('requestId');

                    sfr.feiRequest = frc;

                    FeiAddressContainer fac = new FeiAddressContainer();
                    fac.hostpoint = (String)options.get('hostpoint');
                    fac.entrypoint = (String)options.get('entrypoint');
                    fac.endpoint = (String)options.get('endpoint');
                    fac.identity = (String)options.get('identity');

                    sfr.feiAddress = fac;

                    Http h = new Http();
                    HttpRequest req = new HttpRequest();
                    
                    req.setEndpoint('callout:SendFEIRequest/api/v1/fea-bridge');
                    req.setMethod('POST');
                    req.setTimeout(120000);
                    req.setHeader('Content-Type','application/json');
                    req.setHeader('Accept','application/json');
                    
                    req.setBody(JSON.serialize(sfr));

                    HttpResponse res = (!Test.isRunningTest()) ? h.send(req) : new HttpResponse();
                    
                    System.debug('response body --> '+res.getBody());

                    SendFeiRequestResponse response = (SendFeiRequestResponse)JSON.deserialize(res.getBody(), SendFeiRequestResponse.class);
    
                    String result = landingPage + response.uuid;
                    //System.debug('resultFEAURL '+response.bdy.forwardEndpoint);
                    output.put('resultFEAURL',result);

                }else{
                    output.put('resultFEAURL',originalString);
                }

            }
        }catch(Exception ex){
            System.debug('CtrlFEIUtils Exception --> '+ex.getMessage());
            System.debug('CtrlFEIUtils Exception --> '+ex.getStackTraceString());
            
            output.put('resultFEAURL',originalString);
        }

        return resultString;

    }
    
    @testVisible
    private String sendLINKPOSTRequest(Map<String, Object> input, Map<String, Object> output, Map<String, Object> options){

        String originalString = (Test.isRunningTest()) ? 'TEST' : (String)options.get('originalString');
        String resultString = originalString;
        
        try{
            
            String landingPage = (String)options.get('landingPage');
            
            if(Test.isRunningTest() || (options.containsKey('feiRequestPayload') && options.get('feiRequestPayload') != null)){
                
                Map<String, Object> feiRequestPayload = new Map<String, Object>();

                try{
                    feiRequestPayload = (Test.isRunningTest()) ? new Map<String, Object>{'test'=>'string'} : (Map<String, Object>)options.get('feiRequestPayload');
                }catch(Exception ex){
                    System.debug('container exception : '+ex.getMessage());
                    System.debug('container exception : '+ex.getStackTraceString());
                    try{

                        feiRequestPayload = (Test.isRunningTest()) ? new Map<String, Object>{'test'=>'string'} : (Map<String, Object>)JSON.deserializeUntyped((String)options.get('feiRequestPayload'));
                    }catch(Exception innerEx){
                        System.debug('inner exception : '+innerEx.getMessage());
                        System.debug('inner exception : '+innerEx.getStackTraceString());
                    }
                }
                
                //System.debug('stringPayload '+stringPayload);

                if(feiRequestPayload != null){

                    SendFeiRequest sfr = new SendFeiRequest();

                    FeiRequestContainer frc = new FeiRequestContainer();
                    frc.requestCode = (String)options.get('requestCode');
                    //frc.feiRequest = (Map<String, Object>)JSON.deserializeUntyped(stringPayload);
                    frc.feiRequest = feiRequestPayload;
                    frc.requestId = (String)options.get('requestId');

                    sfr.feiRequest = frc;

                    FeiAddressContainer fac = new FeiAddressContainer();
                    fac.hostpoint = '';
                    fac.entrypoint = '';
                    fac.endpoint = '';
                    fac.identity = '';

                    sfr.feiAddress = fac;

                    Http h = new Http();
                    HttpRequest req = new HttpRequest();
                    
                    req.setEndpoint('callout:SendFEIRequest/api/v1/fea-bridge');
                    req.setMethod('POST');
                    req.setTimeout(120000);
                    req.setHeader('Content-Type','application/json');
                    req.setHeader('Accept','application/json');
                    
                    req.setBody(JSON.serialize(sfr));

                    HttpResponse res = (!Test.isRunningTest()) ? h.send(req) : new HttpResponse();
                    
                    System.debug('response body --> '+res.getBody());

                    SendFeiRequestResponse response = (SendFeiRequestResponse)JSON.deserialize(res.getBody(), SendFeiRequestResponse.class);
    
                    String result = landingPage + response.uuid;
                    
                    //System.debug('resultLINKPOSTURL '+response.bdy.forwardEndpoint);
                    output.put('resultLINKPOSTURL',result);

                }else{
                    output.put('resultLINKPOSTURL',originalString);
                }

            }
        }catch(Exception ex){
            System.debug('CtrlFEIUtils Exception --> '+ex.getMessage());
            System.debug('CtrlFEIUtils Exception --> '+ex.getStackTraceString());
            
            output.put('resultLINKPOSTURL',originalString);
        }

        return resultString;

    }

}