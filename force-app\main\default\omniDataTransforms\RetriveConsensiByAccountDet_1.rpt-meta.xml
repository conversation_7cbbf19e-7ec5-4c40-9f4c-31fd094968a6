<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;DataMapperExtractAction1&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>RetriveConsensiByAccountDet</name>
    <nullInputsIncludedInOutput>true</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem14</globalKey>
        <inputFieldName>AccountDetail:Relation__r.FinServ__RelatedAccount__r.ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>compagniaCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem13</globalKey>
        <inputFieldName>AccountDetail:SourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>NULL</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem12</globalKey>
        <inputFieldName>AccountDetail:OptOutEffectiveDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(MM/dd/yyyy)</outputFieldFormat>
        <outputFieldName>OptOutEffectiveDate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem11</globalKey>
        <inputFieldName>AccountDetail:Relation__r.FinServ__RelatedAccount__r.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>NULL</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem10</globalKey>
        <inputFieldName>AccountDetail:SourceSystemConsentCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>SourceSystemConsentCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>NULL</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem9</globalKey>
        <inputFieldName>ConsentStartDate</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>SourceSystemConsentEffectiveDate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>NULL</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem8</globalKey>
        <inputFieldName>AccountDetail:OptOutEndDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(MM/dd/yyyy)</outputFieldFormat>
        <outputFieldName>OptOutEndDate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>NULL</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem7</globalKey>
        <inputFieldName>AccountDetail:OptOutType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptOutType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>N/D</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem6</globalKey>
        <inputFieldName>JsonDeserializeCheckbox</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>OptInInformation</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:SourceSystemConsentEffectiveDate__c ISBLANK &quot;-&quot; | | var:AccountDetail:SourceSystemConsentEffectiveDate__c 8 10 SUBSTRING &quot;/&quot; + | var:AccountDetail:SourceSystemConsentEffectiveDate__c 5 7 SUBSTRING + &quot;/&quot; + | var:AccountDetail:SourceSystemConsentEffectiveDate__c 0 4 SUBSTRING + CONCAT IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(AccountDetail:SourceSystemConsentEffectiveDate__c),&quot;-&quot;,CONCAT(SUBSTRING(AccountDetail:SourceSystemConsentEffectiveDate__c, 8, 10) + &quot;/&quot; + SUBSTRING(AccountDetail:SourceSystemConsentEffectiveDate__c, 5, 7) + &quot;/&quot; + SUBSTRING(AccountDetail:SourceSystemConsentEffectiveDate__c, 0, 4)))</formulaExpression>
        <formulaResultPath>ConsentStartDate</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem5</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetailMDM:AccountDetailsNPI__c</filterValue>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem4</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>AccountDetailsNPI__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailMDMNPI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetail:Relation__c</filterValue>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem3</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailMDM</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>NULL</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem15</globalKey>
        <inputFieldName>AccountDetail:SourceSystemConsentEndDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>SourceSystemConsentEndDate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;MDM&apos;</filterValue>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem2</globalKey>
        <inputFieldName>RecordType.Name</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailMDM</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>RecordId</filterValue>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem1</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:AccountDetailMDMNPI:OptInInformation__c DESERIALIZE</formulaConverted>
        <formulaExpression>DESERIALIZE(AccountDetailMDMNPI:OptInInformation__c)</formulaExpression>
        <formulaResultPath>JsonDeserializeCheckbox</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>RetriveConsensiByAccountDetCustom0jI9V000000wiqHUAQItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveConsensiByAccountDetCustom2026</globalKey>
        <inputFieldName>AccountDetail:Relation__r.FinServ__Account__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveConsensiByAccountDet</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;RecordId&quot; : &quot;a1i9X000006spIDQAY&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>RetriveConsensiByAccountDet_6</uniqueName>
    <versionNumber>6.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
