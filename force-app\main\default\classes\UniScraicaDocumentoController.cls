global with sharing class UniScraicaDocumentoController implements System.Callable {

    // Entry point for OmniStudio Remote Action
    global Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>) args.get('input');
        Map<String, Object> output = (Map<String, Object>) args.get('output');
        Map<String, Object> options = (Map<String, Object>) args.get('options');

        Object result = invokeMethod(action, input, output);
        System.debug('///Result: ' + result);

        return result;
    }

    // Dispatcher method to route requested actions
    public Boolean invokeMethod(String methodName, Map<String, Object> inputs, Map<String, Object> output) {
        Boolean result = true;
        try {
            if (methodName == 'encryptData') {
                String plainText = (String) inputs.get('plainText');
                String encrypted = encryptData(plainText);
                output.put('encryptedText', encrypted);
            } else if (methodName == 'decryptData') {
                String encryptedBase64 = (String) inputs.get('encryptedBase64');
                String decrypted = decryptData(encryptedBase64);
                output.put('decryptedText', decrypted);
            } else if (methodName == 'encryptAndUrlEncodeData') {
                String plainText = (String) inputs.get('plainText');
                String encryptedUrlEncoded = encryptAndUrlEncodeData(plainText);
                output.put('encryptedUrlEncodedText', encryptedUrlEncoded);
            } else if (methodName == 'composeQueryString') {
                Map<String, Object> params = (Map<String, Object>) inputs.get('queryStringMap');
                Map<String, String> paramsString = new Map<String, String>();
                for (String key : params.keySet()) {
                    String value = (String) params.get(key);
                    paramsString.put(key, value);
                }
                String queryString = composeQueryString(paramsString);
                System.debug('### Composed Query String: ' + queryString);
                output.put('queryString', queryString);
            } else if (methodName == 'composeQueryStringDownloadDocument') {
                String params = (String) inputs.get('query');
                String encoded = EncodingUtil.urlEncode(params, 'UTF-8');
                output.put('queryString', encoded);
            } else if (methodName == 'composeFeiRequestPayloadDownloadDocumenti') {
                String params = (String) inputs.get('query');
                Map<String,String> feiRequestPayload = composeFeiRequestPayloadDownloadDocumenti(params);
                output.put('payload', feiRequestPayload);
            } else {
                output.put('errorMessage', 'Unrecognized method: ' + methodName);
                result = false;
            }
        } catch (Exception e) {
            output.put('errorMessage', 'An unexpected error occurred.');
            UniLogger.writeERROR('Exception in UniScraicaDocumentoController', e);
            result = false;
        }
        return result;
    }

    // Encrypts plain text using AES128 and returns a Base64-encoded string
    public static String encryptData(String plainText) {
        try {
            Id systemAdminProfileId = [SELECT Id FROM Profile WHERE Name IN ('System Administrator','Amministratore del sistema') LIMIT 1].Id;
            UniKeysManager__c settings = UniKeysManager__c.getInstance(systemAdminProfileId);
            System.debug('CS: ' + settings);
            String base64SecretKey = settings.SecretKey__c;
            Blob key = EncodingUtil.base64Decode(base64SecretKey);
            Blob data = Blob.valueOf(plainText);
            Blob encrypted = Crypto.encryptWithManagedIV('AES256', key, data);
            return EncodingUtil.base64Encode(encrypted);
        } catch (Exception exc) {
            UniLogger.writeERROR('Encryption error', exc);
            return null;
        }
    }

    // Decrypts a Base64-encoded string using AES128 and returns plain text
    public static String decryptData(String encryptedBase64) {
        try {
            Id systemAdminProfileId = [SELECT Id FROM Profile WHERE Name IN ('System Administrator','Amministratore del sistema') LIMIT 1].Id;
            UniKeysManager__c settings = UniKeysManager__c.getInstance(systemAdminProfileId);
            String base64Key = settings.SecretKey__c;
            Blob key = EncodingUtil.base64Decode(base64Key);
            Blob encrypted = EncodingUtil.base64Decode(encryptedBase64);
            Blob decrypted = Crypto.decryptWithManagedIV('AES256', key, encrypted);
            return decrypted.toString();

        } catch (Exception exc) {
            UniLogger.writeERROR('Decryption error', exc);
            return null;
        }
    }

    public static String encryptAndUrlEncodeData(String plainText) {
        try {
            Id systemAdminProfileId = [SELECT Id FROM Profile WHERE Name IN ('System Administrator','Amministratore del sistema') LIMIT 1].Id;
            UniKeysManager__c settings = UniKeysManager__c.getInstance(systemAdminProfileId);
            System.debug('CS: ' + settings);
            String base64SecretKey = settings.SecretKey__c;
            Blob key = EncodingUtil.base64Decode(base64SecretKey);
            Blob data = Blob.valueOf(plainText);
            Blob encrypted = Crypto.encryptWithManagedIV('AES256', key, data);
            String encryptedString = EncodingUtil.base64Encode(encrypted);
            return EncodingUtil.urlEncode(encryptedString, 'UTF-8');
        } catch (Exception exc) {
            UniLogger.writeERROR('Encryption and URLEncoding error', exc);
            return null;
        }
    }

    public static String composeQueryString(Map<String, String> params) {
        try {
            List<String> queryParts = new List<String>();
            for (String key : params.keySet()) {
                String value = params.get(key);
                // URL encode both key and value
                System.debug('### PreEncode: key: ' + key + ', value: ' + value);
                String encodedKey = EncodingUtil.urlEncode(key, 'UTF-8');
                String encodedValue = EncodingUtil.urlEncode(value, 'UTF-8');
                System.debug('### PostEncode: key: ' + encodedKey + ', value: ' + encodedValue);
                queryParts.add(encodedKey + '=' + encodedValue);
                System.debug('### QueryPart: ' + queryParts);
            }
            String queryString = String.join(queryParts, '&');
            System.debug('### Final Query String: ' + queryString);
            return queryString;
        } catch (Exception exc) {
            UniLogger.writeERROR('Compose query string error', exc);
            return null;
        }
    }

    public static Map<String,String> composeFeiRequestPayloadDownloadDocumenti(String params) {
        try {
            if (String.isBlank(params)) {
                return null;
            }
            Map<String,String> feiRequestPayload = new Map<String,String>();
            System.debug('### Params: ' + params);
            List<String> keyValuePairs = params.split('&');
            for (String KeyValuePair : keyValuePairs) {
                System.debug('### KeyValuePair: ' + KeyValuePair);
                feiRequestPayload.put(KeyValuePair.split('=', 2)[0], (KeyValuePair.split('=', 2).size() > 1) ? EncodingUtil.urlEncode(KeyValuePair.split('=', 2)[1], 'UTF-8') : null);
                //feiRequestPayload.put(KeyValuePair.split('=', 2)[0], (KeyValuePair.split('=', 2).size() > 1) ? KeyValuePair.split('=', 2)[1] : null);
            }
            System.debug('### feiRequestPayload: ' + feiRequestPayload);
            return feiRequestPayload;
        } catch (Exception exc) {
            UniLogger.writeERROR('Compose query string error', exc);
            return null;
        }
    }
}