<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>DA_Dati_Agenzia_Modifica/Unipolsai/1.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:null,&quot;value&quot;:{},&quot;orderBy&quot;:{},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Attivita.Modifica.RecordId&quot;,&quot;val&quot;:&quot;a1i9X000006b2zOQAQ&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;userId&quot;,&quot;val&quot;:&quot;{User.userId}&quot;,&quot;id&quot;:0},{&quot;name&quot;:&quot;Attivita.Modifica.AtecoCode&quot;,&quot;val&quot;:&quot;null&quot;,&quot;id&quot;:1}]},&quot;state0element11block_element2block_element0_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;RestPut_AggiornamentoInfoSocietarie&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;codiceCompagnia&quot;:&quot;{Compagnia}&quot;,&quot;sito&quot;:&quot;{SitoWeb}&quot;,&quot;tipoInserimento&quot;:&quot;M&quot;,&quot;ciu&quot;:&quot;{Ciu}&quot;,&quot;recordId&quot;:&quot;{Attivita.Modifica.RecordId}&quot;,&quot;atecoSae&quot;:&quot;{Attivita.Modifica.AtecoSAE}&quot;,&quot;atecoRae&quot;:&quot;{Attivita.Modifica.AtecoRAE}&quot;,&quot;atecoActivity&quot;:&quot;{Attivita.Modifica.AtecoActivityType}&quot;,&quot;atecoShortCode&quot;:&quot;{Attivita.Modifica.AtecoShortCode}&quot;,&quot;atecoPrimario&quot;:&quot;{atecoPrimario}&quot;,&quot;userId&quot;:&quot;{userId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;SitoWeb\&quot;:\&quot;{SitoWeb}\&quot;,\&quot;tipoInserimento\&quot;:\&quot;{tipoInserimento}\&quot;,\&quot;Attivita.Modifica.RecordId\&quot;:\&quot;{Attivita.Modifica.RecordId}\&quot;,\&quot;Attivita.Modifica.AtecoSAE\&quot;:\&quot;{Attivita.Modifica.AtecoSAE}\&quot;,\&quot;Attivita.Modifica.AtecoRAE\&quot;:\&quot;{Attivita.Modifica.AtecoRAE}\&quot;,\&quot;Attivita.Modifica.AtecoShortCode\&quot;:\&quot;{Attivita.Modifica.AtecoShortCode}\&quot;,\&quot;Attivita.Modifica.AtecoActivityType\&quot;:\&quot;{Attivita.Modifica.AtecoActivityType}\&quot;,\&quot;atecoPrimario\&quot;:\&quot;{atecoPrimario}\&quot;,\&quot;userId\&quot;:\&quot;{userId}\&quot;,\&quot;Compagnia\&quot;:\&quot;{Compagnia}\&quot;,\&quot;Ciu\&quot;:\&quot;{Ciu}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;SitoWeb&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;tipoInserimento&quot;,&quot;val&quot;:&quot;M&quot;,&quot;id&quot;:10},{&quot;name&quot;:&quot;Attivita.Modifica.RecordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:17},{&quot;name&quot;:&quot;Attivita.Modifica.AtecoSAE&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:18},{&quot;name&quot;:&quot;Attivita.Modifica.AtecoRAE&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:19},{&quot;name&quot;:&quot;Attivita.Modifica.AtecoShortCode&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:20},{&quot;name&quot;:&quot;Attivita.Modifica.AtecoActivityType&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:24},{&quot;name&quot;:&quot;atecoPrimario&quot;,&quot;val&quot;:&quot;{Attivita.Modifica.AtecoCode}&quot;,&quot;id&quot;:21},{&quot;name&quot;:&quot;userId&quot;,&quot;val&quot;:&quot;{User.userId}&quot;,&quot;id&quot;:23},{&quot;name&quot;:&quot;Compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:24},{&quot;name&quot;:&quot;Ciu&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:25}]}}</dataSourceConfig>
    <description>Simone Di Biagio:</description>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>DA_Informazioni_Societarie_Modifica</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;,&quot;label&quot;:&quot;around:none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-around_none &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_bottom slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cspan%20style=%22font-size:%2014pt;%22%3EModifica%20informazioni%20societarie%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_bottom slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_1_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-color_error%22%3EAggiornamento%20Sito%20Web:%20%7BerroreSito%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-69&quot;,&quot;field&quot;:&quot;erroreSito&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;erroreSito&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-4&quot;,&quot;field&quot;:&quot;erroreSito&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-31&quot;,&quot;field&quot;:&quot;erroreSito&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot; \&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-175&quot;,&quot;field&quot;:&quot;erroreSito&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Text-0&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_1_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-color_error%22%3EAggiornamento%20Ateco:%20%7BerroreAteco%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-81&quot;,&quot;field&quot;:&quot;erroreAteco&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-115&quot;,&quot;field&quot;:&quot;erroreAteco&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-116&quot;,&quot;field&quot;:&quot;erroreAteco&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-117&quot;,&quot;field&quot;:&quot;erroreAteco&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-132&quot;,&quot;field&quot;:&quot;erroreAteco&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot; \&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Text-1&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_outputField_2_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-color_error%22%3EAggiornamento%20Dati%20Societari:%20%7BerroreUpdate%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-1&quot;,&quot;field&quot;:&quot;erroreUpdate&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-47&quot;,&quot;field&quot;:&quot;erroreUpdate&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-48&quot;,&quot;field&quot;:&quot;erroreUpdate&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-49&quot;,&quot;field&quot;:&quot;erroreUpdate&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot; \&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-91&quot;,&quot;field&quot;:&quot;erroreUpdate&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Text-2&quot;}],&quot;elementLabel&quot;:&quot;Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_2_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Ch4%20class=%22slds-text-heading_medium%22%3ECodice%20Attivit&amp;agrave;%3C/h4%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_3_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Codice ATECO&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{Attivita.Modifica.AtecoShortCode}&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.AtecoShortCode}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.ATECOValues}&quot;,&quot;id&quot;:0}],&quot;required&quot;:true,&quot;readOnly&quot;:true},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;InfoSocietarie.esitoCrif&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-4-Select-0&quot;},{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Codice ATECO&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{Attivita.Modifica.AtecoShortCode}&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.AtecoShortCode}&quot;,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.ATECOValues}&quot;,&quot;id&quot;:0}],&quot;required&quot;:true},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;InfoSocietarie.esitoCrif&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-4-Select-0&quot;,&quot;key&quot;:&quot;element_element_block_3_0_baseInputElement_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;}],&quot;elementLabel&quot;:&quot;Block-3&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_4_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;SAE&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{Attivita.Modifica.AtecoSAE}&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.AtecoSAE}&quot;,&quot;required&quot;:true,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.SAEValues}&quot;,&quot;id&quot;:0}],&quot;readOnly&quot;:true},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-28&quot;,&quot;field&quot;:&quot;InfoSocietarie.esitoCrif&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_4_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Select-0&quot;},{&quot;key&quot;:&quot;element_element_block_4_0_baseInputElement_1_0&quot;,&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;SAE&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{Attivita.Modifica.AtecoSAE}&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.AtecoSAE}&quot;,&quot;required&quot;:true,&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.SAEValues}&quot;,&quot;id&quot;:0}]},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-35&quot;,&quot;field&quot;:&quot;InfoSocietarie.esitoCrif&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_4_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Select-0-clone-0&quot;}],&quot;elementLabel&quot;:&quot;Block-4&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;RAE&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{Attivita.Modifica.AtecoRAE}&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.AtecoRAE}&quot;,&quot;required&quot;:true,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.RAEValues}&quot;,&quot;id&quot;:0}],&quot;readOnly&quot;:true},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-14&quot;,&quot;field&quot;:&quot;InfoSocietarie.esitoCrif&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;}},&quot;elementLabel&quot;:&quot;Block-3-Select-1&quot;,&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;},{&quot;name&quot;:&quot;Select&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;RAE&quot;,&quot;type&quot;:&quot;combobox&quot;,&quot;fieldBinding&quot;:&quot;{Attivita.Modifica.AtecoRAE}&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.AtecoRAE}&quot;,&quot;required&quot;:true,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{Attivita.Modifica.RAEValues}&quot;,&quot;id&quot;:0}]},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-21&quot;,&quot;field&quot;:&quot;InfoSocietarie.esitoCrif&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;}},&quot;elementLabel&quot;:&quot;Block-5-clone-0-Select-0-clone-2&quot;,&quot;key&quot;:&quot;element_element_block_5_0_baseInputElement_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;}],&quot;elementLabel&quot;:&quot;Block-5&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_5_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Ch4%20class=%22slds-text-heading_medium%22%3ESito%20Web%3C/h4%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-5-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-6&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-52&quot;,&quot;field&quot;:&quot;erroreFormattazioneSito&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;null&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-65&quot;,&quot;field&quot;:&quot;erroreFormattazioneSito&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-72&quot;,&quot;field&quot;:&quot;erroreFormattazioneSito&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot;\&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-81&quot;,&quot;field&quot;:&quot;erroreFormattazioneSito&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;\&quot; \&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-101&quot;,&quot;field&quot;:&quot;erroreFormattazioneSito&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;undefined&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;},{&quot;id&quot;:&quot;state-new-condition-120&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_7_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-color_destructive%20slds-text-align_center%22%3EErrore%20Formattazione%20Sito:%20%7BerroreFormattazioneSito%7D%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_7_0&quot;,&quot;elementLabel&quot;:&quot;Block-7-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-7&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_6_0_block_0_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Sito Web&quot;,&quot;fieldBinding&quot;:&quot;{SitoWeb}&quot;,&quot;value&quot;:&quot;{SitoWeb}&quot;,&quot;required&quot;:false},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;TipoInserimento&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;Crif&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;}},&quot;elementLabel&quot;:&quot;Block-6-Text-0&quot;,&quot;key&quot;:&quot;element_element_element_block_6_0_block_0_0_baseInputElement_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_6_0_block_0_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Sito Web&quot;,&quot;fieldBinding&quot;:&quot;{SitoWeb}&quot;,&quot;value&quot;:&quot;{SitoWeb}&quot;,&quot;required&quot;:false,&quot;readOnly&quot;:true},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;TipoInserimento&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;Crif&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;}},&quot;elementLabel&quot;:&quot;Block-6-Block-1-Text-0-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_6_0_block_0_0_baseInputElement_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_6_0_block_0_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_6_0&quot;,&quot;elementLabel&quot;:&quot;Block-6-Block-1&quot;},{&quot;key&quot;:&quot;element_element_block_6_0_block_1_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:6},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Tipo&quot;,&quot;fieldBinding&quot;:&quot;{TipoInserimento}&quot;,&quot;value&quot;:&quot;-&quot;,&quot;required&quot;:false,&quot;readOnly&quot;:true},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-21&quot;,&quot;field&quot;:&quot;TipoInserimento&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;M&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;}},&quot;elementLabel&quot;:&quot;Block-6-Text-2&quot;,&quot;key&quot;:&quot;element_element_element_block_6_0_block_1_0_baseInputElement_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_6_0_block_1_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Tipo&quot;,&quot;fieldBinding&quot;:&quot;&quot;,&quot;value&quot;:&quot;Manuale&quot;,&quot;required&quot;:false,&quot;readOnly&quot;:true,&quot;placeholder&quot;:&quot;Manuale&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-14&quot;,&quot;field&quot;:&quot;TipoInserimento&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;M&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;}},&quot;elementLabel&quot;:&quot;Block-6-Block-2-Text-0-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_6_0_block_1_0_baseInputElement_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_6_0_block_1_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_6_0&quot;,&quot;elementLabel&quot;:&quot;Block-6-Block-2&quot;}],&quot;elementLabel&quot;:&quot;Block-8&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_7_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Ch4%20class=%22slds-text-heading_medium%22%3EInformazioni%20Societarie%3C/h4%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_7_0&quot;,&quot;elementLabel&quot;:&quot;Block-7-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-9&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_8_0_customLwc_0_0&quot;,&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;setInit&quot;:&quot;1&quot;,&quot;atecoPrimarioCode&quot;:&quot;{Attivita.Modifica.AtecoCode}&quot;,&quot;atecoShortCode&quot;:&quot;{Attivita.Modifica.AtecoShortCode}&quot;,&quot;saeCodeOpt&quot;:&quot;{Attivita.Modifica.SAEValues}&quot;,&quot;atecoSaeCode&quot;:&quot;{Attivita.Modifica.AtecoSAE}&quot;,&quot;esitoCrif&quot;:&quot;{InfoSocietarie.esitoCrif}&quot;,&quot;descAtecoRiqualificato&quot;:&quot;{InfoSocietarie.descrizioneAtecoRiqualificato}&quot;,&quot;infoError&quot;:&quot;{InfoSocietarie.error}&quot;,&quot;infoSocietarie&quot;:&quot;{InfoSocietarie}&quot;,&quot;customlwcname&quot;:&quot;atecoPrimario&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;}},&quot;parentElementKey&quot;:&quot;element_block_8_0&quot;,&quot;elementLabel&quot;:&quot;Block-8-Custom LWC-1&quot;}],&quot;elementLabel&quot;:&quot;Block-10&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_top&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;     border-top: #cccccc 2px solid; \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_9_0_block_0_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Annulla&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1737373827502&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;close_modal&quot;,&quot;composed&quot;:true,&quot;message&quot;:&quot;close&quot;,&quot;bubbles&quot;:true},&quot;key&quot;:&quot;1737365556305-32w0nij91&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}},&quot;flyoutChannel&quot;:&quot;&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-3-Action-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_2_0_block_0_0_action_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_0_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_9_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_9_0_block_1_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_2_0_block_1_0_action_0_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Elimina&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741969361559&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;close_modal&quot;,&quot;cardName&quot;:&quot;DA_Informazioni_Societarie_Elimina&quot;,&quot;flyoutLwc&quot;:&quot;DA_Informazioni_Societarie_Elimina&quot;,&quot;cardNode&quot;:&quot;{record}&quot;},&quot;key&quot;:&quot;1741948242206-nhjtlooxx&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;displayAsButton&quot;:true,&quot;hideActionIcon&quot;:true,&quot;buttonVariant&quot;:&quot;destructive&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;center&quot;}},&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;},&quot;flyoutChannel&quot;:&quot;close_modal&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;center&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-1-Action-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;center&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;parentElementKey&quot;:&quot;element_block_9_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;2&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_2-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_9_0_block_2_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Salva&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741948400782-j1vthd4mw&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747994043065&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;responseNode&quot;:&quot;&quot;,&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;RestPut_AggiornamentoInfoSocietarie\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;codiceCompagnia\&quot;:\&quot;{Compagnia}\&quot;,\&quot;sito\&quot;:\&quot;{SitoWeb}\&quot;,\&quot;tipoInserimento\&quot;:\&quot;M\&quot;,\&quot;ciu\&quot;:\&quot;{Ciu}\&quot;,\&quot;recordId\&quot;:\&quot;{Attivita.Modifica.RecordId}\&quot;,\&quot;atecoSae\&quot;:\&quot;{Attivita.Modifica.AtecoSAE}\&quot;,\&quot;atecoRae\&quot;:\&quot;{Attivita.Modifica.AtecoRAE}\&quot;,\&quot;atecoActivity\&quot;:\&quot;{Attivita.Modifica.AtecoActivityType}\&quot;,\&quot;atecoShortCode\&quot;:\&quot;{Attivita.Modifica.AtecoShortCode}\&quot;,\&quot;atecoPrimario\&quot;:\&quot;{atecoPrimario}\&quot;,\&quot;userId\&quot;:\&quot;{userId}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;SitoWeb\\\&quot;:\\\&quot;{SitoWeb}\\\&quot;,\\\&quot;tipoInserimento\\\&quot;:\\\&quot;{tipoInserimento}\\\&quot;,\\\&quot;Attivita.Modifica.RecordId\\\&quot;:\\\&quot;{Attivita.Modifica.RecordId}\\\&quot;,\\\&quot;Attivita.Modifica.AtecoSAE\\\&quot;:\\\&quot;{Attivita.Modifica.AtecoSAE}\\\&quot;,\\\&quot;Attivita.Modifica.AtecoRAE\\\&quot;:\\\&quot;{Attivita.Modifica.AtecoRAE}\\\&quot;,\\\&quot;Attivita.Modifica.AtecoShortCode\\\&quot;:\\\&quot;{Attivita.Modifica.AtecoShortCode}\\\&quot;,\\\&quot;Attivita.Modifica.AtecoActivityType\\\&quot;:\\\&quot;{Attivita.Modifica.AtecoActivityType}\\\&quot;,\\\&quot;atecoPrimario\\\&quot;:\\\&quot;{atecoPrimario}\\\&quot;,\\\&quot;userId\\\&quot;:\\\&quot;{userId}\\\&quot;,\\\&quot;Compagnia\\\&quot;:\\\&quot;{Compagnia}\\\&quot;,\\\&quot;Ciu\\\&quot;:\\\&quot;{Ciu}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;SitoWeb\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:8},{\&quot;name\&quot;:\&quot;tipoInserimento\&quot;,\&quot;val\&quot;:\&quot;M\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;Attivita.Modifica.RecordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:17},{\&quot;name\&quot;:\&quot;Attivita.Modifica.AtecoSAE\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:18},{\&quot;name\&quot;:\&quot;Attivita.Modifica.AtecoRAE\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:19},{\&quot;name\&quot;:\&quot;Attivita.Modifica.AtecoShortCode\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:20},{\&quot;name\&quot;:\&quot;Attivita.Modifica.AtecoActivityType\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:24},{\&quot;name\&quot;:\&quot;atecoPrimario\&quot;,\&quot;val\&quot;:\&quot;{Attivita.Modifica.AtecoCode}\&quot;,\&quot;id\&quot;:21},{\&quot;name\&quot;:\&quot;userId\&quot;,\&quot;val\&quot;:\&quot;{User.userId}\&quot;,\&quot;id\&quot;:23},{\&quot;name\&quot;:\&quot;Compagnia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:24},{\&quot;name\&quot;:\&quot;Ciu\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:25}]}&quot;},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true},{&quot;key&quot;:&quot;*************-m32f3d8d3&quot;,&quot;label&quot;:&quot;Refresh&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Record&quot;:{&quot;targetName&quot;:&quot;AccountDetails__c&quot;,&quot;targetAction&quot;:&quot;view&quot;,&quot;targetId&quot;:&quot;{Attivita.Modifica.RecordId}&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;brand&quot;,&quot;ariaLabel&quot;:&quot;Action&quot;,&quot;flyoutDetails&quot;:{},&quot;iconOnly&quot;:true,&quot;disabled&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-3-Action-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_9_0_block_2_0_action_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_9_0_block_2_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_9_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-11&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_top&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;     border-top: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:null,&quot;value&quot;:{},&quot;orderBy&quot;:{},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Attivita.Modifica.RecordId&quot;,&quot;val&quot;:&quot;a1i9X000006b2zOQAQ&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;userId&quot;,&quot;val&quot;:&quot;{User.userId}&quot;,&quot;id&quot;:0},{&quot;name&quot;:&quot;Attivita.Modifica.AtecoCode&quot;,&quot;val&quot;:&quot;null&quot;,&quot;id&quot;:1}]},&quot;title&quot;:&quot;DA_Informazioni_Societarie_Modifica&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfDA_Informazioni_Societarie_Modifica_13_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9V000003du8fSAA&quot;,&quot;MasterLabel&quot;:&quot;cfDA_Informazioni_Societarie_Modifica_13_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;events&quot;:[{&quot;eventname&quot;:&quot;seleziona&quot;,&quot;channelname&quot;:&quot;AtecoPrimarioLwc&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1745486400928-azj40vzby&quot;,&quot;label&quot;:&quot;Seleziona ateco primario&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747840168760&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;atecoPrimario&quot;,&quot;fieldValue&quot;:&quot;{action.value}&quot;},{&quot;fieldName&quot;:&quot;Attivita.Modifica.AtecoShortCode&quot;,&quot;fieldValue&quot;:&quot;{action.atecoShortCode}&quot;},{&quot;fieldName&quot;:&quot;Attivita.Modifica.SAEValues&quot;,&quot;fieldValue&quot;:&quot;{action.atecoSaeOpt}&quot;},{&quot;fieldName&quot;:&quot;Attivita.Modifica.AtecoSAE&quot;,&quot;fieldValue&quot;:&quot;{action.atecoSae}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;AtecoPrimarioLwc:seleziona&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}],&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;DA_Informazioni_Societarie_Modifica&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56},&quot;sessionVars&quot;:[]}</propertySetConfig>
    <versionNumber>13</versionNumber>
</OmniUiCard>
