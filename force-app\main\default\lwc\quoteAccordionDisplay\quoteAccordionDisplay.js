import { LightningElement, api, track,wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';
import { registerRefreshContainer, REFRESH_ERROR, REFRESH_COMPLETE, REFRESH_COMPLETE_WITH_ERRORS } from 'lightning/refresh';
import areas_of_need_images from '@salesforce/resourceUrl/areas_of_need';
import getStoredQuotesInfo from '@salesforce/apex/AccordionController.getStoredQuotesInfo';
import retrieveProducts from '@salesforce/apex/STProductTabsetController.retrieveProducts';
import { publish, MessageContext } from 'lightning/messageService';
import FEI_CHANNEL from '@salesforce/messageChannel/FeiContainerChannel__c';
import getPermissionSetName    from '@salesforce/apex/FeiHelper.getPermissionSetName';
import getFederationIdentifier from '@salesforce/apex/FeiHelper.getFederationIdentifier';
import getCustomerAndAgencyIds from '@salesforce/apex/FeiHelper.getCustomerAndAgencyIds';

// FEI flow constants
const PREVENTIVI_FLOW = 'FEIQuickActionPreventivi';
const FEI_PREVENTIVI_VIEW = {
  ESSIG_AUTO:            'IP.CRUSCOTTO.DISPATCH',
  ESSIG_RE:              'RE.CRUSCOTTO.DISPATCH',
  ESSIG_VITA:            'VITA.PROPOSTA',            
  ESSIG_VITA_PREVIDENZA: 'IP.CRUSCOTTO.DISPATCH'
};
const TITLES_PREVENTIVI_VIEW = {
  ESSIG_AUTO:            'Visualizza preventivo',
  ESSIG_RE:              'Visualizza preventivo',
  ESSIG_VITA:            'Visualizza preventivo',   
  ESSIG_VITA_PREVIDENZA: 'Visualizza preventivo'
};
// map per il “modify” FEI
const FEI_PREVENTIVI_MODIFY = {
  ESSIG_AUTO:            'NPAC.CRUSCOTTO.DISPATCH',
  ESSIG_RE:              null,
  ESSIG_VITA:            'VITA.PROPOSTA',
  ESSIG_VITA_PREVIDENZA: 'RE.CRUSCOTTO.DISPATCH',
  ESSIG_VITA_INDIVIDUALE:'VITA.PROPOSTA'
};
// titoli per il “modify”
const TITLES_PREVENTIVI_MODIFY = {
  ESSIG_AUTO:            'Definisci in Polizza',
  ESSIG_RE:              'Modifica preventivo',
  ESSIG_VITA:            'Modifica preventivo',
  ESSIG_VITA_PREVIDENZA: 'Riprendi preventivo',
  ESSIG_VITA_INDIVIDUALE:'Riprendi preventivo'
};

const ESSIG_DOMAINS = new Set(['ESSIG_AUTO','ESSIG_RE','ESSIG_VITA','ESSIG_VITA_PREVIDENZIALE','ESSIG_VITA_INDIVIDUALE']);
const MIN_SCREEN_SIZE_FOR_PC = 1200;

// Constants for image URLs
const BASE = areas_of_need_images + '/areas_of_need/on';
const PET_IMG        = BASE + '/pet.png';
const HOUSE_IMG      = BASE + '/casa.png';
const FAMILY_IMG     = BASE + '/famiglia.png';
const INJURIES_IMG   = BASE + '/infortuni.png';
const MOTORCYCLE_IMG = BASE + '/mobilita.png';
const HEALTH_IMG     = BASE + '/salute.png';
const CAR_IMG        = BASE + '/veicoli.png';
const TRAVEL_IMG     = BASE + '/viaggi.png';
const VITA_IMG       = BASE + '/vita.png';

export default class QuoteAccordionDisplay extends NavigationMixin(LightningElement) {
    @api recordId;
    
    @api isStored = false;
    @api source;
    @track isLoading = true;
    @track quotes = [];
    @track hasQuote = false;
    @track isTablet = window.innerWidth <= MIN_SCREEN_SIZE_FOR_PC;
     // Flow & preview
  @track showFEI            = false;
  @track showPreview;
  @track flowName           = 'FEIQuickActionUNICA';
  @track flowTitle          = '';
  @track flowInputVariables = [];
  @track currentPreviewUrl     = '';
  @track currentPreviewDomain  = '';
  @track currentQuotes = [];
  @track storedQuotes = [];
  @track hasStoredQuotes = false;
@track isFeiTypeModify     = false;
@track feiTypeModify       = 'MODIFICA_PREVENTIVO';
@track feiTypeView         = 'CONSULTAZIONE_CONTRATTO';

@track flowTitleModifica   = 'Modifica Preventivo';
@track flowTitleVisualizza = 'Visualizza Preventivo';

    _refreshContainerId;
  get unicaLabel() {
    const all = this.currentQuotes.length ? this.currentQuotes : this.storedQuotes;
    if (!all.length) {
      return 'UNICA';
    }
    const raw = (all[0].domainType || '').trim().toUpperCase();
    return ESSIG_DOMAINS.has(raw) ? 'ESSIG' : 'UNICA';
  }


    @api
    get targetId() {
        return this.recordId;
    }
    set targetId(val) {
        this.recordId = val;
    }


      @wire(MessageContext) messageContext;

  @track storedOpen = false;
get storedSectionIcon() {
  return this.storedOpen ? 'utility:chevrondown' : 'utility:chevronright';
}
toggleStoredSection() {
  this.storedOpen = !this.storedOpen;
}

    @api
    get quoteStored() {
        return this.isStored;
    }
    set quoteStored(val) {
        this.isStored = val;
    }

    @api
    get productChannel() {
        return this.source;
    }
    set productChannel(val) {
        this.source = val;
    }
  get isPrevidenza() {
    return this.source === 'Preventivatore Previdenza';
  }

async connectedCallback() {
  console.log('✅ recordId ricevuto dal FlexCard in pre:', this.recordId);
  await new Promise(resolve => setTimeout(resolve, 3000));
console.log('✅ recordId ricevuto dal FlexCard:', this.recordId);
  window.addEventListener('resize', this.handleResize);
  this._refreshContainerId = registerRefreshContainer(this, this.refreshContainer);
  
  await this.fetchQuotes();
}



    disconnectedCallback() {
        window.removeEventListener('resize', this.handleResize);
    }

    handleResize = () => {
        this.isTablet = window.innerWidth <= MIN_SCREEN_SIZE_FOR_PC;
    }

   fetchQuotes() {
  this.isLoading = true;

  // 👉 Recupera il primo prodotto collegato
  retrieveProducts({ recordId: this.recordId })
    .then(products => {
      if (!products || !products.length) {
        console.warn('❗ Nessun prodotto trovato per recordId:', this.recordId);
        this.productRecordId = null;
        return;
      }

      this.productRecordId = products[0].id;
      console.log('🧩 Prodotto trovato:', this.productRecordId);

      // Qui parte la tua logica esistente con this.productRecordId al posto di this.recordId
      const pCurrent = getStoredQuotesInfo({
        recordId: this.productRecordId,
        isStored: false
      });

      const pStored = getStoredQuotesInfo({
        recordId: this.productRecordId,
        isStored: true
      });

      return Promise.all([pCurrent, pStored]).then(([dataCurrent, dataStored]) => {
        console.log('📨 current:', dataCurrent);
        console.log('📨 stored:', dataStored);

        this.currentQuotes = dataCurrent.map(qd => this._mapQuote(qd));
        this.storedQuotes  = dataStored .map(qd => this._mapQuote(qd));

        this.hasQuote        = this.currentQuotes.length > 0;
        this.hasStoredQuotes = this.storedQuotes.length  > 0;
      });
    })
    .catch(err => {
      console.error('❌ Errore fetchQuotes:', err);
      this.showToast('Errore', 'Contattare l’amministratore di sistema.', 'error');
    })
    .finally(() => {
      this.isLoading = false;
    });
}

// helper privato che estrae la logica di mapping
_mapQuote(qd) {
  const opportunityCoverages = qd.opportunityCoverages.map((cov, idx) => ({
    key: `${cov.areaOfNeed}-${cov.amount}-${idx}`,
    areaIcon: this.convertAreaOfNeedToImage(cov.areaOfNeed),
    assetItems: cov.areaOfNeed === 'Famiglia' ? ['Famiglia'] : cov.assets,
    descriptionItems: cov.description,
    amount: cov.amount,
    stage: cov.stage,
    fractionation: cov.fractionation,
    conventions: cov.conventions || '-',
    fullName: cov.fullName || `${cov.firstName} ${cov.lastName}`,
    targetProduct: cov.targetProduct,
    ral: cov.ral,
    yearlyGrowth: cov.yearlyGrowth,
    previdentialGap: cov.previdentialGap,
    retirementYear: cov.retirementYear,
    numOfChildren: cov.numOfChildren,
    sector: cov.sector
  }));

  return {
    recordId:          qd.recordId,
    name:              qd.name,
    status:            qd.commercialStatus,
    totalAmount:       qd.totalAmount,
    monthlyContribution: qd.monthlyContribution,
    digitalStep:       qd.digitalStep || '-',
    source:            qd.source,
    cip:               qd.cip,
    creationDate:      qd.creationDate,
    expirationDate:    qd.expirationDate,
    unicaLink:         qd.unicaLink,
    documentUrl:       qd.documentUrl,
    isOpen:            false,
    isStored:          qd.isStored,
    domainType:        qd.domainType,
    areasOfNeedImages: this.convertAreasOfNeedToImages(qd.areasOfNeed),
    opportunityCoverages
  };
}

/** Restituisce l’array di URL immagini per gli ambiti */
convertAreasOfNeedToImages(areas = []) {
  return areas
    .map(a => this.convertAreaOfNeedToImage(a))
    .filter(u => u); // scarta eventuali null
}


convertAreaOfNeedToImage(area) {
  switch ((area||'').trim()) {
    case 'Cane e Gatto': return PET_IMG;
    case 'Casa':         return HOUSE_IMG;
    case 'Famiglia':     return FAMILY_IMG;
    case 'Infortuni':    return INJURIES_IMG;
    case 'Mobilita':     return MOTORCYCLE_IMG;
    case 'Salute':       return HEALTH_IMG;
    case 'Veicoli':      return CAR_IMG;
    case 'Viaggio':      return TRAVEL_IMG;
    case 'Vita':         return VITA_IMG;
    case 'Previdenza integrativa': return VITA_IMG;
    case 'Persona':return VITA_IMG;
    case 'Casa e Famiglia': return VITA_IMG;
    default:             return null;
  }
}



    toggleSection(event) {
        const id = event.currentTarget.dataset.id;
        // Aggiorna currentQuotes
        this.currentQuotes = this.currentQuotes.map(q => {
            if (q.recordId === id) {
                return { ...q, isOpen: !q.isOpen };
            }
            return q;
        });
        // Aggiorna storedQuotes
        this.storedQuotes = this.storedQuotes.map(q => {
            if (q.recordId === id) {
                return { ...q, isOpen: !q.isOpen };
            }
            return q;
        });
    }


    handleQuoteClick(event) {
        event.stopPropagation();
        const quoteId = event.currentTarget.dataset.id;
        if (quoteId) {
            this[NavigationMixin.Navigate]({
                type: 'standard__recordPage',
                attributes: { recordId: quoteId, actionName: 'view' }
            });
        }
    }

  handleUnicaClick(event) {
    const recId = event.currentTarget.dataset.id;
    const q = this.quotes.find(x => x.recordId === recId);
    if (q && q.unicaLink) {
      window.open(q.unicaLink, '_blank');
    }
  }

async myPDFPreview(event) {
        const recId = event.currentTarget.dataset.id;
        const q = this.currentQuotes.concat(this.storedQuotes).find(x => x.recordId === recId);
        if (!q) return;

        const dt = q.domainType?.trim().toUpperCase();
        const hasDoc = !!q.documentUrl;

        // UNICA PDF preview
        if ((dt === 'PU' || dt === 'ESSIG_RE') && hasDoc) {
            this.currentPreviewUrl = q.documentUrl;
            this.currentPreviewDomain = q.domainType;
            this.openPDFPreview();
        }
        // ESSIG_AUTO: publish LMS payload
        else if (dt === 'ESSIG_AUTO') {
            try {
                const [fiscalCode, permissionSetName] = await Promise.all([
                    getFederationIdentifier(),
                    getPermissionSetName()
                ]);
                const { customerId, agencyId } = await getCustomerAndAgencyIds({ quoteId: q.recordId });

                const feiRequestPayload = {
                    tipoOperazione:  'SS',
                    compagnia:       customerId,
                    agenzia:         agencyId,
                    progressivo:     '1',
                    numeroArchivio:  q.SourceSystemIdentifier0Formula__c,
                    numeroVersione:  q.SourceSystemIdentifier1Formula__c
                };

                publish(this.messageContext, FEI_CHANNEL, {
                    recordId:          q.recordId,
                    FEIID:             'RA.STAMPA.DIFFERITA',
                    FiscalCode:        fiscalCode,
                    permissionSetName: permissionSetName,
                    society:           'SOC_1',
                    feiRequestPayload: feiRequestPayload
                });
                console.log('📤 Published ESSIG_AUTO LMS message');
            } catch (err) {
                console.error('Errore FEI ESSIG_AUTO:', err);
                this.showToast('Errore FEI', err.body?.message || err.message || err, 'error');
            }
        }
        // Other ESSIG flows → existing logic
        else {
            console.log('myPDFPreview: LANCIO FEI PREVIEW');
            this.handleManageFEI(q);
        }
    }

handleManageFEI(event) {
  const recId = event.currentTarget.dataset.id;
  const q = this.currentQuotes.concat(this.storedQuotes)
             .find(x => x.recordId === recId);
  if (!q) return;
  const dt = q.domainType?.trim().toUpperCase();
  if (dt === 'PU') {
    this.handleVisualizzaClick(q);
  } else {
    this.handleVisualizzaClickPDF(q);
  }
}


handleVisualizzaClick(q) {
  this.isFeiTypeModify = false;
  this.flowInputVariables = [
    { name: 'recordId', type: 'String', value: q.recordId },
    { name: 'feiType',   type: 'String', value: this.feiTypeView }
  ];
  this.flowTitle = this.flowTitleVisualizza;
  this.showFEI   = true;
}

handleVisualizzaClickPDF(q) {
  const key  = (q.domainType||'').trim().toUpperCase();
  const map  = this.isFeiTypeModify ? FEI_PREVENTIVI_MODIFY : FEI_PREVENTIVI_VIEW;
  const tit  = this.isFeiTypeModify ? TITLES_PREVENTIVI_MODIFY : TITLES_PREVENTIVI_VIEW;

  if (!map[key]) {
    console.warn(`FEI non valido: ${key}`);
    return;
  }
  this.flowName = PREVENTIVI_FLOW;
  this.flowTitle = tit[key];
  this.flowInputVariables = [
    { name: 'recordId', type: 'String', value: q.recordId },
    { name: 'FEIID',    type: 'String', value: map[key] }
  ];
  if (key === 'ESSIG_VITA_INDIVIDUALE') {
    this.flowInputVariables.push({ name:'society', type:'String', value:'SOC_1' });
  }
  this.showFEI = true;
}


    // invece di this.quote.recordId:
invokeFlow(q) {
  this.flowInputVariables = [
    { name: 'recordId', type: 'String', value: q.recordId },
    { name: 'feiType',   type: 'String', value: this.isFeiTypeModify ? this.feiTypeModify : this.feiTypeView }
  ];
  this.flowTitle = this.isFeiTypeModify ? this.flowTitleModifica : this.flowTitleVisualizza;
  this.showFEI = true;
}



    openPDFPreview()  { this.showPreview = true; }
  closePDFPreview() { this.showPreview = false; }


  // chiude il flow container
  handleClose() {
    this.showFEI = false;
  }


    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({ title, message, variant }));
    }

    refreshContainer(refreshPromise) {
        console.log("Refreshing QuoteAccordionDisplay (simplified)");
        this.fetchQuotes();
        return refreshPromise.then(status => {
            if (status === REFRESH_COMPLETE) {
                console.log("Refresh Done!");
            } else if (status === REFRESH_COMPLETE_WITH_ERRORS) {
                console.warn("Refresh Done with issues");
            } else if (status === REFRESH_ERROR) {
                console.error("Refresh Major error");
            }
        });
    }
}