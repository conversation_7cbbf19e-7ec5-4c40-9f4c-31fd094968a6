<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <choices>
        <name>AgencyChoice</name>
        <choiceText>Agenzia</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Agenzia</stringValue>
        </value>
    </choices>
    <choices>
        <name>CCchoice</name>
        <choiceText>Contact Center</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Contact Center</stringValue>
        </value>
    </choices>
    <decisions>
        <name>agencyNullCheck</name>
        <label>agencyNullCheck</label>
        <locationX>710</locationX>
        <locationY>782</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>agencyNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getAgency</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>agencyNullScreen</targetReference>
            </connector>
            <label>agencyNull</label>
        </rules>
        <rules>
            <name>agencyNotNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getAgency</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getAgents</targetReference>
            </connector>
            <label>agencyNotNull</label>
        </rules>
    </decisions>
    <decisions>
        <name>agentNullCheck</name>
        <label>agentNullCheck</label>
        <locationX>710</locationX>
        <locationY>998</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>agentsNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getAgents</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>agentsNullSCreen</targetReference>
            </connector>
            <label>agentsNull</label>
        </rules>
        <rules>
            <name>agentsNotNull</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getAgents</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>agentChoiceScreen</targetReference>
            </connector>
            <label>agentsNotNull</label>
        </rules>
    </decisions>
    <decisions>
        <name>ccORAgencyDecision</name>
        <label>ccORAgencyDecision</label>
        <locationX>1304</locationX>
        <locationY>566</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>agencyAssignment</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Scegli_a_chi_assegnare_l_attivit</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Agenzia</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getAgency</targetReference>
            </connector>
            <label>agencyAssignment</label>
        </rules>
        <rules>
            <name>ccAssignment</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Scegli_a_chi_assegnare_l_attivit</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Contact Center</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>WIPScreen</targetReference>
            </connector>
            <label>ccAssignment</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckAuthorized</name>
        <label>CheckAuthorized</label>
        <locationX>1733</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>NotAuthorizedScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Authorized</defaultConnectorLabel>
        <rules>
            <name>Authorized</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>getCase.AssignedTo__c</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <elementReference>$User.Id</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Case_Handling_SubFlow_Check_User.isAuthorized</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ScreenCCAgencyChoiche</targetReference>
            </connector>
            <label>Authorized</label>
        </rules>
    </decisions>
    <decisions>
        <name>notesPresentCheck</name>
        <label>notesPresentCheck</label>
        <locationX>710</locationX>
        <locationY>1538</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>withNotes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>textForNote</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>createNote</targetReference>
            </connector>
            <label>withNotes</label>
        </rules>
    </decisions>
    <description>Update getAgent with running user id</description>
    <environments>Default</environments>
    <interviewLabel>Riassegna {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Case Handling - Riassegna</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>createContentDocumentLink</name>
        <label>createContentDocumentLink</label>
        <locationX>578</locationX>
        <locationY>1754</locationY>
        <inputAssignments>
            <field>ContentDocumentId</field>
            <value>
                <elementReference>createNote</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LinkedEntityId</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ShareType</field>
            <value>
                <stringValue>V</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Visibility</field>
            <value>
                <stringValue>AllUsers</stringValue>
            </value>
        </inputAssignments>
        <object>ContentDocumentLink</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>createNote</name>
        <label>createNote</label>
        <locationX>578</locationX>
        <locationY>1646</locationY>
        <connector>
            <targetReference>createContentDocumentLink</targetReference>
        </connector>
        <inputAssignments>
            <field>Content</field>
            <value>
                <elementReference>textForNote</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Title</field>
            <value>
                <stringValue>Nota Riassegnazione</stringValue>
            </value>
        </inputAssignments>
        <object>ContentNote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>getAgency</name>
        <label>getAgency</label>
        <locationX>710</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>agencyNullCheck</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$User.IdAzienda__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getAgents</name>
        <label>getAgents</label>
        <locationX>710</locationX>
        <locationY>890</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>agentNullCheck</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IdAzienda__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getAgency.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getCase</name>
        <label>getCase</label>
        <locationX>1733</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Case_Handling_SubFlow_Check_User</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>updateAssigneeOnCase</name>
        <label>updateAssigneeOnCase</label>
        <locationX>710</locationX>
        <locationY>1322</locationY>
        <connector>
            <targetReference>Case_Handling_Activity_History_Flow_1</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getCase.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>AssignedTo__c</field>
            <value>
                <elementReference>agentsTable.firstSelectedRow.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <screens>
        <name>agencyNullScreen</name>
        <label>agencyNullScreen</label>
        <locationX>50</locationX>
        <locationY>890</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>errorScreenNullAgency</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Nessuna agenzia trovata per l&apos;utente&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>agentChoiceScreen</name>
        <label>agentChoiceScreen</label>
        <locationX>710</locationX>
        <locationY>1106</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>notesScreen</targetReference>
        </connector>
        <fields>
            <name>agentsTableChoiceText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Seleziona il nuovo assegnatario per l&apos;attivitá&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>agentsTable</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>User</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Seleziona l&apos;agente per l&apos;assegnazione</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>getAgents</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;LastName&quot;,&quot;guid&quot;:&quot;column-7eb9&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Cognome&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Last Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;FirstName&quot;,&quot;guid&quot;:&quot;column-46ec&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Nome&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;First Name&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>agentsNullSCreen</name>
        <label>agentsNullSCreen</label>
        <locationX>314</locationX>
        <locationY>1106</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>agentsNullText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Non ci sono agenti a cui riassegnare l&apos;attivitá&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>NotAuthorizedScreen</name>
        <label>NotAuthorizedScreen</label>
        <locationX>2162</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>Copy_1_of_wipText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Non sei autorizzato a Riassegnare l&apos;attività di contatto &lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>notesScreen</name>
        <label>notesScreen</label>
        <locationX>710</locationX>
        <locationY>1214</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>updateAssigneeOnCase</targetReference>
        </connector>
        <fields>
            <name>notesTitledisplay</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Inserisci note relative all&apos;attivitá&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>textForNote</name>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>ScreenCCAgencyChoiche</name>
        <label>ScreenCCAgencyChoiche</label>
        <locationX>1304</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>ccORAgencyDecision</targetReference>
        </connector>
        <fields>
            <name>choiceText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Scegli se riassegnare l&apos;attivitá all&apos;agenzia o al Contact Center&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>ScreenCCAgencyChoiche_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenCCAgencyChoiche_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenCCAgencyChoiche_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Scegli_a_chi_assegnare_l_attivit</name>
                    <choiceReferences>AgencyChoice</choiceReferences>
                    <choiceReferences>CCchoice</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Scegli a chi assegnare l&apos;attivitá</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenCCAgencyChoiche_Section1_Column3</name>
                <fieldType>Region</fieldType>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>4</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>WIPScreen</name>
        <label>WIPScreen</label>
        <locationX>1634</locationX>
        <locationY>674</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>wipText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Contact Center non disponibile&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>1607</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>getCase</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Case_Handling_Activity_History_Flow_1</name>
        <label>Case Handling - Activity History Flow 1</label>
        <locationX>710</locationX>
        <locationY>1430</locationY>
        <connector>
            <targetReference>notesPresentCheck</targetReference>
        </connector>
        <flowName>Case_Handling_Activity_History</flowName>
        <inputAssignments>
            <name>ActionType</name>
            <value>
                <stringValue>Riassegna</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>AssignedTo</name>
            <value>
                <elementReference>agentsTable.firstSelectedRow.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>RecordCase</name>
            <value>
                <elementReference>getCase</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <subflows>
        <name>Case_Handling_SubFlow_Check_User</name>
        <label>Case Handling SubFlow Check User</label>
        <locationX>1733</locationX>
        <locationY>242</locationY>
        <connector>
            <targetReference>CheckAuthorized</targetReference>
        </connector>
        <flowName>Case_Handling_SubFlow_Check_User</flowName>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <variables>
        <name>contentNoteForCase</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>ContentNote</objectType>
    </variables>
    <variables>
        <name>groupIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>noteRecordForCase</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Note</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
