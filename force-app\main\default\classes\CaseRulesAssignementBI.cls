public without sharing class CaseRulesAssignementBI {
    
    // Cache statiche per performance
    private static Boolean sObjectAccessCache;
    private static Map<String, Boolean> objectAccessCache = new Map<String, Boolean>();
    
    static {
        // Inizializza cache al caricamento della classe
        initializeAccessCache();
    }
    
    private static void initializeAccessCache() {
        objectAccessCache.put('FinServ__AccountAccountRelation__c', Schema.sObjectType.FinServ__AccountAccountRelation__c.isAccessible());
        objectAccessCache.put('AccountAgencyDetails__c', Schema.sObjectType.AccountAgencyDetails__c.isAccessible());
        objectAccessCache.put('Group__c', Schema.sObjectType.Group__c.isAccessible());
        objectAccessCache.put('User', Schema.sObjectType.User.isAccessible());
    }
    
    private static Boolean isObjectAccessible(String objectName) {
        return objectAccessCache.get(objectName) ?? false;
    }

    public static void manageCaseGroupsOwnership(List<Case> newListCase) {
        if (newListCase == null || newListCase.isEmpty()) {
            return;
        }
        
        // Classificazione ottimizzata in un singolo loop
        PolicyKey.CaseClassificationResult result = classifyCasesOptimized(newListCase);

        // Early returns per performance
        if (result.cipSet.isEmpty() && result.fiscalCodes.isEmpty() && 
            result.r01r07Cases.isEmpty() && result.r02r08Cases.isEmpty() && 
            result.casesWithPolicy.isEmpty()) {
            return;
        }

        // Caricamento dati parallelo (dove possibile)
        Map<String, Id> cipToGroupId = result.cipSet.isEmpty() ? new Map<String, Id>() : fetchGroupCip(result.cipSet);
        Map<String, User> userMap = result.fiscalCodes.isEmpty() ? new Map<String, User>() : getUserByComponent(result.fiscalCodes);

        // Processamento policy ottimizzato
        Map<String, InsurancePolicy> policyMap = new Map<String, InsurancePolicy>();
        if (!result.casesWithPolicy.isEmpty()) {
            policyMap = fetchPoliciesByBusinessKeyOptimized(result.casesWithPolicy);
            populateInsurancePolicyFieldOptimized(result.casesWithPolicy, policyMap);
        }

        // Preparazione regole di assegnazione ottimizzata
        PolicyKey.AssignmentRuleMaps ruleMaps = prepareAssignmentRuleMapsOptimized(result.r01r07Cases, result.r02r08Cases, policyMap);

        // Context unico per tutte le assegnazioni
        PolicyKey.AssignmentContextData ctx = new PolicyKey.AssignmentContextData(cipToGroupId, userMap, ruleMaps);

        // Assegnazione ottimizzata
        assignCasesOptimized(newListCase, ctx);
    }

    private static PolicyKey.CaseClassificationResult classifyCasesOptimized(List<Case> newListCase) {
        PolicyKey.CaseClassificationResult result = new PolicyKey.CaseClassificationResult();

        for (Case c : newListCase) {
            // Classificazione DrAssegnazione (sempre processata)
            classifyDrAssegnazione(c, result);
            
            // Pre-check per evitare processing inutile di CLAIM/LIQUIDO
            Boolean isClaimOrLiquido = 'CLAIM'.equalsIgnoreCase(c.DrContextEntity__c) || 'LIQUIDO'.equalsIgnoreCase(c.DrCanale__c);
            
            // Assignment Rules (solo per non-CLAIM/LIQUIDO per R02/R08)
            classifyAssignmentRuleOptimized(c, result, isClaimOrLiquido);
            
            // Policy Cases (esclude CLAIM e LIQUIDO)
            if (!isClaimOrLiquido) {
                classifyPolicyCase(c, result);
            }
        }

        return result;
    }
    
    private static void classifyAssignmentRuleOptimized(Case c, PolicyKey.CaseClassificationResult result, Boolean isClaimOrLiquido) {
        if (c.TECH_AssignmentRules__c == 'R01' || c.TECH_AssignmentRules__c == 'R07') {
            result.r01r07Cases.add(c);
        } else if ((c.TECH_AssignmentRules__c == 'R02' || c.TECH_AssignmentRules__c == 'R08') && !isClaimOrLiquido) {
            result.r02r08Cases.add(c);
        }
    }

    private static void classifyDrAssegnazione(Case c, PolicyKey.CaseClassificationResult result) {
        if (c.DrAssegnazione__c == '1' && c.DrAssegnatario__c != null && c.DrAgenziaFiglia__c != null) {
            result.cipSet.add('CIP_SOC_' + c.DrAssegnazione__c + '_AGE_' + c.DrAgenziaFiglia__c + '_' + c.DrAssegnatario__c);
        } else if (c.DrAssegnazione__c == '3' && !String.isBlank(c.DrAssegnatario__c)) {
            result.fiscalCodes.add(c.DrAssegnatario__c);
        }
    }

    private static void classifyPolicyCase(Case c, PolicyKey.CaseClassificationResult result) {
        if (!String.isBlank(c.BusinessKey__c) && !'CLAIM'.equalsIgnoreCase(c.DrContextEntity__c)
            && (c.DrCanale__c == null || !'LIQUIDO'.equalsIgnoreCase(c.DrCanale__c))) {
                result.casesWithPolicy.add(c);
        }
    }

    private static PolicyKey.AssignmentRuleMaps prepareAssignmentRuleMapsOptimized(List<Case> r01r07Cases, List<Case> r02r08Cases, Map<String, InsurancePolicy> policyMap) {
        // Processamento parallelo dove possibile
        Map<String, String> cipMapR01R07 = new Map<String, String>();
        Map<String, Id> cipToGroupIdR01R07 = new Map<String, Id>();
        if (!r01r07Cases.isEmpty()) {
            cipMapR01R07 = resolveR01R07Optimized(r01r07Cases);
            if (!cipMapR01R07.isEmpty()) {
                cipToGroupIdR01R07 = fetchGroupCip(new Set<String>(cipMapR01R07.values()));
            }
        }

        Map<String, String> cipMapR02R08 = new Map<String, String>();
        Map<String, Id> cipToGroupIdR02R08 = new Map<String, Id>();
        if (!r02r08Cases.isEmpty()) {
            cipMapR02R08 = resolveR02R08Optimized(r02r08Cases, policyMap);
            if (!cipMapR02R08.isEmpty()) {
                cipToGroupIdR02R08 = fetchGroupCip(new Set<String>(cipMapR02R08.values()));
            }
        }

        return new PolicyKey.AssignmentRuleMaps(cipMapR01R07, cipToGroupIdR01R07, cipMapR02R08, cipToGroupIdR02R08);
    }
    
    private static void assignCasesOptimized(List<Case> newListCase, PolicyKey.AssignmentContextData ctx) {
        for (Case c : newListCase) {
            if (String.isBlank(c.DrAssegnazione__c)) {
                continue; // Skip se DrAssegnazione non è valorizzato
            }
            
            Boolean assigned = false;
            switch on c.DrAssegnazione__c {
                when '1' {
                    assigned = assignType1Optimized(c, ctx);
                }
                when '2' {
                    assigned = assignByAssignmentRulesOptimized(c, ctx.ruleMaps);
                }
                when '3' {
                    assigned = assignType3Optimized(c, ctx);
                }
            }
        }
    }
    
    private static Boolean assignType1Optimized(Case c, PolicyKey.AssignmentContextData ctx) {
        if (String.isBlank(c.DrAgenziaFiglia__c) || String.isBlank(c.DrAssegnatario__c)) {
            return assignByAssignmentRulesOptimized(c, ctx.ruleMaps);
        }
        
        String cip = 'CIP_SOC_' + c.DrAssegnazione__c + '_AGE_' + c.DrAgenziaFiglia__c + '_' + c.DrAssegnatario__c;
        Id groupId = ctx.cipToGroupId.get(cip);
        if (groupId != null) {
            c.AssignedGroup__c = groupId;
            return true;
        }
        return assignByAssignmentRulesOptimized(c, ctx.ruleMaps);
    }
    
    private static Boolean assignType3Optimized(Case c, PolicyKey.AssignmentContextData ctx) {
        if (String.isBlank(c.DrAssegnatario__c)) {
            return assignByAssignmentRulesOptimized(c, ctx.ruleMaps);
        }
        
        User u = ctx.userMap.get(c.DrAssegnatario__c);
        if (u != null) {
            c.AssignedTo__c = u.Id;
            return true;
        }
        return assignByAssignmentRulesOptimized(c, ctx.ruleMaps);
    }
    
    private static Boolean assignByAssignmentRulesOptimized(Case c, PolicyKey.AssignmentRuleMaps maps) {
        String rule = c.TECH_AssignmentRules__c;
        
        if ((rule == 'R01' || rule == 'R07') && !maps.cipMapR01R07.isEmpty()) {
            PolicyKey.AssignmentContext ctx = new PolicyKey.AssignmentContext(c, maps.cipMapR01R07, maps.cipToGroupIdR01R07);
            return assignFromAccountAgencyOptimized(ctx);
        }
        
        if ((rule == 'R02' || rule == 'R08') && !maps.cipMapR02R08.isEmpty()) {
            PolicyKey.AssignmentContext ctx = new PolicyKey.AssignmentContext(c, maps.cipMapR02R08, maps.cipToGroupIdR02R08);
            return assignFromBusinessKeyOptimized(ctx);
        }
        
        return false;
    }
    
    private static Boolean assignFromAccountAgencyOptimized(PolicyKey.AssignmentContext ctx) {
        if (ctx.c.AccountId == null || ctx.c.Agency__c == null || String.isBlank(ctx.c.LeoActivityCode__c)) {
            return false;
        }
        
        String key = ctx.c.LeoActivityCode__c + '-' + ctx.c.AccountId + '-' + ctx.c.Agency__c;
        String cip = ctx.cipMap.get(key);
        if (String.isBlank(cip)) {
            return false;
        }
        
        Id groupId = ctx.cipToGroupId.get(cip);
        if (groupId == null) {
            return false;
        }
        
        ctx.c.AssignedGroup__c = groupId;
        return true;
    }
    
    private static Boolean assignFromBusinessKeyOptimized(PolicyKey.AssignmentContext ctx) {
        // Pre-check per CLAIM/LIQUIDO ottimizzato
        if ('CLAIM'.equalsIgnoreCase(ctx.c.DrContextEntity__c) || 'LIQUIDO'.equalsIgnoreCase(ctx.c.DrCanale__c)) {
            return false;
        }
        
        if (String.isBlank(ctx.c.BusinessKey__c) || String.isBlank(ctx.c.LeoActivityCode__c)) {
            return false;
        }
        
        PolicyKey pk = PolicyKey.fromBusinessKey(ctx.c.BusinessKey__c, ctx.c.LeoActivityCode__c);
        if (pk == null) {
            return false;
        }
        
        String cip = ctx.cipMap.get(pk.getReturnKey());
        if (String.isBlank(cip)) {
            return false;
        }
        
        Id groupId = ctx.cipToGroupId.get(cip);
        if (groupId == null) {
            return false;
        }
        
        ctx.c.AssignedGroup__c = groupId;
        return true;
    }

	private static Map<String, String> resolveR01R07Optimized(List<Case> r01r07Cases) {
        Map<String, String> result = new Map<String, String>();
        Map<String, Case> keyToCase = new Map<String, Case>();
        Set<Id> accountIds = new Set<Id>();
        Set<Id> agencyIds = new Set<Id>();

        // Pre-filtering e raccolta IDs ottimizzata
        for (Case c : r01r07Cases) {
            if (c.AccountId != null && c.Agency__c != null && !String.isBlank(c.LeoActivityCode__c)) {
                String accAgeKey = c.AccountId + '-' + c.Agency__c;
                keyToCase.put(accAgeKey, c);
                accountIds.add(c.AccountId);
                agencyIds.add(c.Agency__c);
            }
        }

        if (keyToCase.isEmpty()) { 
            return result; 
        }

        // Check accessibilità con cache
        if (!isObjectAccessible('FinServ__AccountAccountRelation__c') || !isObjectAccessible('AccountAgencyDetails__c')) {
            return result;
        }

        // Query ottimizzata AccountAccountRelation
        Map<String, Id> accAgeKeyToRelId = new Map<String, Id>();
        List<FinServ__AccountAccountRelation__c> relations = [
            SELECT Id, FinServ__Account__c, FinServ__RelatedAccount__c 
            FROM FinServ__AccountAccountRelation__c
            WHERE FinServ__Account__c IN :accountIds AND FinServ__RelatedAccount__c IN :agencyIds
        ];
        
        for (FinServ__AccountAccountRelation__c rel : relations) {
            accAgeKeyToRelId.put(rel.FinServ__Account__c + '-' + rel.FinServ__RelatedAccount__c, rel.Id);
        }

        if (accAgeKeyToRelId.isEmpty()) {
            return result;
        }

        // Query ottimizzata AccountAgencyDetails
        Map<Id, String> relIdToSubAgency = new Map<Id, String>();
        List<AccountAgencyDetails__c> agencyDetails = [
            SELECT SubAgencyCode__c, Relation__c 
            FROM AccountAgencyDetails__c 
            WHERE Relation__c IN :accAgeKeyToRelId.values()
        ];
        
        for (AccountAgencyDetails__c aad : agencyDetails) {
            relIdToSubAgency.put(aad.Relation__c, aad.SubAgencyCode__c);
        }

        // Costruzione risultato ottimizzata
        for (String accAgeKey : keyToCase.keySet()) {
            Id relId = accAgeKeyToRelId.get(accAgeKey);
            String subAgencyCode = relIdToSubAgency.get(relId);
            
            if (relId != null && !String.isBlank(subAgencyCode)) {
                Case c = keyToCase.get(accAgeKey);
                String leo = c.LeoActivityCode__c;
                String cip = 'CIP_SOC_' + c.DrAssegnazione__c + '_AGE_' + c.DrAgenziaFiglia__c + '_' + subAgencyCode;
                result.put(leo + '-' + accAgeKey, cip);
            }
        }

        return result;
    }
	
	private static Map<String, String> resolveR02R08Optimized(List<Case> r02r08Cases, Map<String, InsurancePolicy> policyMap) {
        Map<String, String> result = new Map<String, String>();
        
        for (Case c : r02r08Cases) {
            if (String.isBlank(c.BusinessKey__c) || String.isBlank(c.LeoActivityCode__c)) {
                continue;
            }
            
            PolicyKey pk = PolicyKey.fromBusinessKey(c.BusinessKey__c, c.LeoActivityCode__c);
            if (pk == null) {
                continue;
            }
            
            InsurancePolicy policy = policyMap.get(pk.businessKey);
            if (policy != null && !String.isBlank(policy.CIP__c)) {
                String valueCip = 'CIP_SOC_' + policy.CompanyCode__c + '_AGE_' + c.DrAgenziaFiglia__c + '_00' + policy.CIP__c;
                result.put(pk.getReturnKey(), valueCip);
            }
        }
        
        return result;
    }
	
    private static Map<String, InsurancePolicy> fetchPoliciesByBusinessKeyOptimized(List<Case> cases) {
        // Raggruppamento ottimizzato PolicyKey e AccountId
        Map<String, PolicyKey> businessKeyToPolicyKey = new Map<String, PolicyKey>();
        Set<Id> accountIds = new Set<Id>();
        
        for (Case c : cases) {
            if (!String.isBlank(c.BusinessKey__c)) {
                PolicyKey pk = PolicyKey.fromBusinessKey(c.BusinessKey__c, c.LeoActivityCode__c);
                if (pk != null) {
                    businessKeyToPolicyKey.put(pk.businessKey, pk);
                }
            }
            if (c.AccountId != null) {
                accountIds.add(c.AccountId);
            }
        }

        if (businessKeyToPolicyKey.isEmpty() || accountIds.isEmpty()) {
            return new Map<String, InsurancePolicy>();
        }

        // Query ottimizzata con binding di accountIds
        List<String> conditions = new List<String>();
        for (PolicyKey pk : businessKeyToPolicyKey.values()) {
            conditions.add(pk.toSoqlCondition());
        }

        String query = 'SELECT Id, CIP__c, CompanyCode__c, MotherAgencyCode__c, AgencyCode__c, PolicyBranchCode__c, ' +
                      'ReferencePolicyNumber, NameInsuredId, RecordType.DeveloperName ' +
                      'FROM InsurancePolicy WHERE NameInsuredId IN :accountIds';
        
        if (!conditions.isEmpty()) {
            query += ' AND (' + String.join(conditions, ' OR ') + ')';
        }

        List<InsurancePolicy> policies = Database.query(query);

        // Matching ottimizzato con Map lookup
        Map<String, InsurancePolicy> result = new Map<String, InsurancePolicy>();
        for (InsurancePolicy policy : policies) {
            for (PolicyKey pk : businessKeyToPolicyKey.values()) {
                if (pk.matches(policy)) {
                    result.put(pk.businessKey, policy);
                    break; // Early break per performance
                }
            }
        }

        return result;
    }
    
    private static void populateInsurancePolicyFieldOptimized(List<Case> cases, Map<String, InsurancePolicy> policyMap) {
        if (policyMap == null || policyMap.isEmpty()) {
            return;
        }
        
        // Cache dei RecordType per evitare multiple string comparisons
        Set<String> puRecordTypes = new Set<String>{'PU_FOLDER', 'PU_POSITION'};
        
        for (Case c : cases) {
            if (c.Insurance_Policy__c == null && !String.isBlank(c.BusinessKey__c)) {
                InsurancePolicy policy = policyMap.get(c.BusinessKey__c);
                if (policy != null) {
                    c.Insurance_Policy__c = policy.Id;
                    if (puRecordTypes.contains(policy.RecordType.DeveloperName?.toUpperCase())) {
                        c.DrIdFolderPU__c = 'S';
                    }
                }
            }
        }
    }

	public static Map<String, Id> fetchGroupCip(Set<String> cipSet) {
        Map<String, Id> cipToGroupId = new Map<String, Id>();
        if (!cipSet.isEmpty() && isObjectAccessible('Group__c')) {
            List<Group__c> groups = [SELECT Id, ExternalId__c FROM Group__c WHERE ExternalId__c IN :cipSet];
            for (Group__c g : groups) {
                cipToGroupId.put(g.ExternalId__c, g.Id);
            }
        } 
        return cipToGroupId;
    }

	public static Map<String, User> getUserByComponent(Set<String> groupComponentSet) {
		Map<String, User> userMap = new Map<String, User>();
		if (!groupComponentSet.isEmpty() && isObjectAccessible('User')) {
			List<User> users = [SELECT Id, ExternalId__c FROM User WHERE ExternalId__c IN :groupComponentSet];
			for (User u : users) {
				userMap.put(u.ExternalId__c, u);
			}
		}
		return userMap;
	}

    public static void checkPolicy(List<Case> newListCase) {
        if (newListCase == null || newListCase.isEmpty()) {
            return;
        }
        
        for (Case c : newListCase) {
            if (shouldSetPolicyFlag(c)) {
                c.DrIdFolderPU__c = 'S';
            }
        }
    }
    
    private static Boolean shouldSetPolicyFlag(Case c) {
        // Early returns per performance - controlli più costosi alla fine
        if (c.Insurance_Policy__c != null || c.DrIdFolderPU__c != null) {
            return false;
        }
        
        if (String.isBlank(c.BusinessKey__c) || c.BusinessKey__c.length() != 15) {
            return false;
        }
        
        if (String.isBlank(c.DrPosizioniPU__c)) {
            return false;
        }
        
        try {
            Integer posizioniPU = Integer.valueOf(c.DrPosizioniPU__c);
            return posizioniPU > 0;
        } catch (TypeException e) {
            System.debug('Invalid DrPosizioniPU value for Case ' + c.Id + ': ' + c.DrPosizioniPU__c);
            return false;
        }
    }
}