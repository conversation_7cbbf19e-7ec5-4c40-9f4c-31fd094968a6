import { LightningElement, api, wire } from 'lwc';
import {
    IsConsoleNavigation,
    EnclosingTabId,
    openSubtab,
    openTab 
} from 'lightning/platformWorkspaceApi';
import { NavigationMixin } from 'lightning/navigation';

export default class MenuStrumentiBranch extends NavigationMixin(LightningElement) {
    @wire(IsConsoleNavigation) isConsoleNavigation;
    @wire(EnclosingTabId) enclosingTabId;

    @api userNetwork;

    /**
     * Array dei nodi fornito dal parent
     */
    @api items = [];
    /**
     * Nome della sezione (passato dal container); undefined nei rami annidati
     */
    @api sectionName;
    @api tabName;


    get itemsProcessed() {
        return this.items.map(item => {
            const groups = item?.groupCodes?.split(';') || [];
            const groupFind = groups.filter(group => this.userNetwork?.ucaUser?.groups?.includes(group))
            const hasGroupPermission = groupFind.length > 0;

            const hasValidLink = (['REDIRECT ESTERNO'].includes(item.type) && item.redirectLink) || (
                ['REDIRECT INTERNO', 'FEI'].includes(item.type)
            )

            const oldDisabled = item.disabled;

            const available = (hasGroupPermission || groups.length === 0) && hasValidLink

            console.group('MenuStrumentiBranch:: itemsProcessed', item.developerName);
            console.log('MenuStrumentiBranch:: itemsProcessed::groups', JSON.stringify(groups))
            console.log('MenuStrumentiBranch:: itemsProcessed::groupFind', JSON.stringify(groupFind))
            console.log('MenuStrumentiBranch:: itemsProcessed::userGroups', JSON.stringify(this.userNetwork?.ucaUser?.groups || []));
            console.log('MenuStrumentiBranch:: itemsProcessed::hasGroupPermission', hasGroupPermission);
            console.log('MenuStrumentiBranch:: itemsProcessed::hasValidLink', hasValidLink);
            console.log('MenuStrumentiBranch:: itemsProcessed::hasEmptyGroup', groups.length === 0);
            console.log('MenuStrumentiBranch:: itemsProcessed::available', available);
            console.groupEnd();

            return {
                ...item,
                disabled: !available || oldDisabled,
            }
        });
    }

    connectedCallback() {
        // Registra listener globale su document (fase bubble) per collapse fuori dai toggle
        this._handleOutsideClick = this.handleClickOutside.bind(this);
        document.addEventListener('click', this._handleOutsideClick);
    }

    disconnectedCallback() {
        document.removeEventListener('click', this._handleOutsideClick);
    }

    handleLeafClick(event) {
        // Previeni collapse globale
        event.stopPropagation();

        const key = event.currentTarget.dataset.id;
        const node = this.items.find(n => n.developerName === key);

        console.log('MenuStrumentiBranch:: ' + JSON.stringify(key));
        console.log('MenuStrumentiBranch:: ' + JSON.stringify(node));

        if (!node) return;

        if (node.type === 'FEI') {
            //this.navigate(node.feiId);

            // Introdurre motore FEI
            this.navigateFei(node);

        } else if (node.type === 'REDIRECT INTERNO' && node.redirectLink) {
            this.navigateInterno(node);
        } else if (node.type === 'REDIRECT ESTERNO' && node.redirectLink) {
            this.navigate(node);
        }
    }

    toggleBranch(event) {
        // Previeni collapse globale
        event.stopPropagation();

        const key = event.currentTarget.dataset.id;
        // Apre/chiude il nodo selezionato, chiude tutti gli altri tra gli siblings
        this.items = this.items.map(item => ({
            ...item,
            expanded: item.developerName === key ? !item.expanded : false
        }));

        // Solo se sono un ramo di PRIMO LIVELLO, notifico il container
        if (this.sectionName) {
            this.dispatchEvent(new CustomEvent('branchtoggle', {
                detail: { tab: this.tabName, sectionName: this.sectionName, developerName: key },
                bubbles: true,
                composed: true
            }));
        }
    }

    /**
     * Collassa il branch se il click non è stato intercettato da handleLeafClick o toggleBranch
     */
    handleClickOutside(event) {
        // Se ci sono ancora eventi di click non gestiti, collapse
        if (this.items.some(item => item.expanded)) {
            this.items = this.items.map(item => ({ ...item, expanded: false }));
        }
    }

    handleFavoriteClick(event) {
        // Previeni collapse globale
        event.stopPropagation();

        const key = event.currentTarget.dataset.id;
        const node = this.items.find(n => n.developerName === key);

        console.log('MenuStrumentiBranch:: ' + JSON.stringify(node));

        this.dispatchEvent(new CustomEvent('favoriteselected', {
            detail: { node: node },
            bubbles: true,
            composed: true
        }));
    }

    handleUnfavoriteClick(event) {
        // Previeni collapse globale
        event.stopPropagation();

        const key = event.currentTarget.dataset.id;

        this.dispatchEvent(new CustomEvent('unfavoriteselected', {
            detail: { developerName: key },
            bubbles: true,
            composed: true
        }));
    }

    navigateInterno(node) {
        if (!this.isConsoleNavigation) {
            return;
        }

        let _attributes = {};

        if(node.redirectType == 'standard__app') _attributes.appTarget = node.redirectLink;
        if(node.redirectType == 'standard__component') _attributes.componentName = node.redirectLink;
        if(node.redirectType == 'standard__navItemPage') _attributes.apiName = node.redirectLink;

        console.log(JSON.stringify(_attributes));
        
        
        if (!this.enclosingTabId) {
            openTab({
                pageReference: {
                    type: node.redirectType,
                    attributes: _attributes
                },
                focus: true,
            });
        } else {
            // Open sub tab
            try{
                openSubtab(this.enclosingTabId, {
                    pageReference: {
                        type: node.redirectType,
                        attributes: _attributes
                    }
                });
            } catch (error) {
                console.log(JSON.stringify(error))
            }
        }

        /*this[NavigationMixin.Navigate]({
            type: 'standard__webPage',
            attributes: { url: url.startsWith('/') ? '/' + url : url }
        });*/
    }

    navigate(node) {
        try{
            this[NavigationMixin.Navigate]({
                type: 'standard__webPage',
                attributes: { url: node.redirectLink }
            });
        } catch (error) {
            console.log(JSON.stringify(error))
        }
    }

    navigateFei(node) {

        this.dispatchEvent(new CustomEvent('navigatefei', {
            detail: { node: node },
            bubbles: true,
            composed: true
        }));
    }
}