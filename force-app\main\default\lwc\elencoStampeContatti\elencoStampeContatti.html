<template>
    <c-loader lwc:ref="loader"></c-loader>
    <div class="slds-col slds-p-around_xx-small slds-size_12-of-12">
        <div class="accordion-header accordion" onclick={toggleSection} data-id="stampe">
            <lightning-icon icon-name={iconNameStampe} alternative-text="toggle" size="x-small"></lightning-icon>
            <span class="accordion-label">Elenco Stampe {processType}</span>
        </div>
        <div class="accordion-content" if:true={stampe.isOpen}>
            <lightning-datatable
                key-field="id"
                data={stampe.rows}
                columns={stampe.columns}
                hide-checkbox-column
                resize-column-disabled
                onrowaction={handleRowAction}>
            </lightning-datatable>
        </div>
    </div>
    <template if:true={erroreDownload}>
        <div class="slds-text-color_error slds-m-top_small">Errore durante il download del documento. Riprova più tardi.</div>
    </template>    
    <div class="slds-col slds-p-around_xx-small slds-size_12-of-12">
        <div class="accordion-header accordion" onclick={toggleSection} data-id="contatti">
            <lightning-icon icon-name={iconNameContatti} alternative-text="toggle" size="x-small"></lightning-icon>
            <span class="accordion-label">Storicità Contatti {processType}</span>
        </div>
        <div class="accordion-content" if:true={contatti.isOpen}>
            <lightning-datatable
                key-field="id"
                data={contatti.rows}
                columns={contatti.columns}
                hide-checkbox-column
                resize-column-disabled
                onrowaction={handleRowAction}>
            </lightning-datatable>
        </div>
    </div>
</template>