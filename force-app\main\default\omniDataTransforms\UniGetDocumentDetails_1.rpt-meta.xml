<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;GetDocumentsDetails&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>UniGetDocumentDetails</name>
    <nullInputsIncludedInOutput>true</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| &quot;SELECT/\/\/Name/\/\/FROM/\/\/Unita_Territoriali__c/\/\/WHERE/\/\/RecordType.Name/\/\/=/\/\/&apos;Nazione&apos;AND/\/\/Codice_belfiore__c/\/\/=/\/\/&apos;{0}&apos;/\/\/LIMIT/\/\/1&quot; var:contentVersions:IssuingCountryCode__c QUERY</formulaConverted>
        <formulaExpression>QUERY(&quot;SELECT Name FROM Unita_Territoriali__c WHERE RecordType.Name = &apos;Nazione&apos;AND Codice_belfiore__c = &apos;{0}&apos; LIMIT 1&quot;, %contentVersions:IssuingCountryCode__c%)</formulaExpression>
        <formulaResultPath>statoEsteso</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>UniGetDocumentDetailsCustom1378</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem25</globalKey>
        <inputFieldName>contentVersions:IssuingCountryCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:stato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem26</globalKey>
        <inputFieldName>contentVersions:Status__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:status</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| &quot;SELECT/\/\/Name/\/\/FROM/\/\/Unita_Territoriali__c/\/\/WHERE/\/\/RecordType.Name/\/\/=/\/\/&apos;Comune&apos;AND/\/\/Codice_catasto__c/\/\/=/\/\/&apos;{0}&apos;/\/\/LIMIT/\/\/1&quot; var:contentVersions:IssuingLocalityCode__c QUERY</formulaConverted>
        <formulaExpression>QUERY(&quot;SELECT Name FROM Unita_Territoriali__c WHERE RecordType.Name = &apos;Comune&apos;AND Codice_catasto__c = &apos;{0}&apos; LIMIT 1&quot;, %contentVersions:IssuingLocalityCode__c%)</formulaExpression>
        <formulaResultPath>comuneEsteso</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>UniGetDocumentDetailsCustom127</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem21</globalKey>
        <inputFieldName>accountDetails:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:accountDetailsId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem22</globalKey>
        <inputFieldName>contentVersions:RleaseDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(dd-MM-yyyy)</outputFieldFormat>
        <outputFieldName>documentDetails:dataValidita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem23</globalKey>
        <inputFieldName>contentVersions:IssuingLocalityCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:comuneEmissione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem24</globalKey>
        <inputFieldName>contentVersions:IssuingProvince__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:provincia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom7334</globalKey>
        <inputFieldName>comuneEsteso</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:comuneEsteso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom4671</globalKey>
        <inputFieldName>statoEsteso</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:statoEsteso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem17</globalKey>
        <inputFieldName>nomeCompagnia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:nomeCompagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem18</globalKey>
        <inputFieldName>contentVersions:RecordType.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:tipologia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem19</globalKey>
        <inputFieldName>timestamp</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:timestamp</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem20</globalKey>
        <inputFieldName>contentVersions:Title</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:numeroDocumento</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem13</globalKey>
        <inputFieldName>contentVersions:IssuingBody__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:enteEmissione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom6322</globalKey>
        <inputFieldName>Societa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:codiceCompagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem14</globalKey>
        <inputFieldName>contentVersions:ExpirationDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(dd-MM-yyyy)</outputFieldFormat>
        <outputFieldName>documentDetails:dataScadenza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem15</globalKey>
        <inputFieldName>contentVersions:SourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:externalId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem16</globalKey>
        <inputFieldName>accountDetails:SourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem9</globalKey>
        <inputFieldName>Relation__r.FinServ__Account__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Business&apos;</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem10</globalKey>
        <inputFieldName>RecordType.DeveloperName</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem11</globalKey>
        <inputFieldName>contentVersions:RecordType.DeveloperName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:tipologiaRT</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem12</globalKey>
        <inputFieldName>contentVersions:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>documentDetails:ContentVersionId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>contentLinks:ContentDocumentId</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem5</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>ContentDocument</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>contentDocument</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>contentDocument:LatestPublishedVersionId</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem6</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>ContentVersion</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>contentVersions</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Individual&apos;</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem7</globalKey>
        <inputFieldName>RecordType.DeveloperName</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Societa</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem8</globalKey>
        <inputFieldName>Relation__r.RelatedAccount_ExternalId__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>accountDetails:Id</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem1</globalKey>
        <inputFieldName>LinkedEntityId</inputFieldName>
        <inputObjectName>ContentDocumentLink</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>contentLinks</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Societa</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem2</globalKey>
        <inputFieldName>Relation__r.RelatedAccount_ExternalId__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Societa &quot;SOC_1&quot; == &quot;unipolsai&quot; | var:Societa &quot;SOC_4&quot; == &quot;unisalute&quot; &quot;&quot; IF IF</formulaConverted>
        <formulaExpression>IF(Societa == &quot;SOC_1&quot;, &quot;unipolsai&quot;, 
  IF(Societa == &quot;SOC_4&quot;, &quot;unisalute&quot;, &quot;&quot;)
)</formulaExpression>
        <formulaResultPath>nomeCompagnia</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem4</globalKey>
        <inputFieldName>Relation__r.FinServ__Account__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accountDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| NOW</formulaConverted>
        <formulaExpression>NOW()</formulaExpression>
        <formulaResultPath>timestamp</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>UniGetDocumentDetailsCustom0jI9O000000vDtNUAUItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetDocumentDetails</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;AccountId&quot; : &quot;0019X000015e2ZLQAY&quot;,
  &quot;Societa&quot; : &quot;SOC_1&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>UniGetDocumentDetails_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
