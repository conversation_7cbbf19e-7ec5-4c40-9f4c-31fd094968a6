<template>
  <lightning-card class="custom-card">
    <!-- <PERSON><PERSON> principale -->
    <div class="slds-p-around_medium">
      <h2 class="slds-text-heading_medium" style="font-weight: bold; font-family: sans-serif;">
        Preventivi e lavorazioni
      </h2>
    </div>

    <!-- Spinner di caricamento -->
    <template if:true={isLoading}>
      <div class="slds-p-around_medium">
        <lightning-spinner alternative-text="Caricamento" size="small"></lightning-spinner>
      </div>
    </template>

    <template if:false={isLoading}>
      <!-- Sezione “nessuna quote correnti” -->
      <template if:false={hasQuote}>
        <div class="no-data-container slds-p-around_medium">
          <p>Non sono presenti prodotti correlati alla trattativa.</p>
        </div>
      </template>

      <!-- ========== SEZIONE 1: Preventivi correnti ========== -->
      <template if:true={hasQuote}>
        <div class=" slds-theme_shade slds-p-around_small">
          <!-- HEADER -->
          <div class="slds-grid slds-text-align_center header-row">
            <div class="slds-col slds-size_1-of-24"></div>
            <div class="slds-col slds-size_2-of-24"><strong>Id preventivo</strong></div>
            <div class="slds-col slds-size_1-of-12"><strong>Stato</strong></div>
            <div class="slds-col slds-size_2-of-24"><strong>Ambiti</strong></div>
            <template if:true={isPrevidenza}>
              <div class="slds-col slds-size_1-of-12"><strong>Contribuzione mensile</strong></div>
            </template>
            <template if:false={isPrevidenza}>
              <div class="slds-col slds-size_1-of-12"><strong>Premio</strong></div>
            </template>
            <div class="slds-col slds-size_1-of-12"><strong>Step Digitale</strong></div>
            <div class="slds-col slds-size_1-of-12"><strong>Fonte preventivo</strong></div>
            <div class="slds-col slds-size_1-of-12"><strong>CIP</strong></div>
            <div class="slds-col slds-size_1-of-12"><strong>Data Creazione</strong></div>
            <div class="slds-col slds-size_1-of-12"><strong>Data di Scadenza</strong></div>
            <div class="slds-col slds-size_1-of-12"><strong>{unicaLabel}</strong></div>
            <div class="slds-col slds-size_1-of-12"><strong>Visualizza PDF</strong></div>
          </div>
          <div class="slds-col slds-size_1-of-1" style="border-bottom:1px solid #d8dde6; margin:0 0 8px;"></div>

          <!-- RIGHE QUOTE CORRENTI -->
          <template for:each={currentQuotes} for:item="q">
            <div key={q.recordId} class="quote-container slds-m-top_x-small">
              <!-- RIGA PRINCIPALE -->
              <div class="slds-grid slds-text-align_center data-row">
                <div class="slds-col slds-size_1-of-24">
                  <template if:true={q.isOpen}>
                    <lightning-button-icon
                      variant="bare"
                      icon-name="utility:chevrondown"
                      alternative-text="Chiudi"
                      data-id={q.recordId}
                      onclick={toggleSection}>
                    </lightning-button-icon>
                  </template>
                  <template if:false={q.isOpen}>
                    <lightning-button-icon
                      variant="bare"
                      icon-name="utility:chevronright"
                      alternative-text="Apri"
                      data-id={q.recordId}
                      onclick={toggleSection}>
                    </lightning-button-icon>
                  </template>
                </div>
                <div class="slds-col slds-size_2-of-24">
                  <a role="button"
                    tabindex="0"
                    data-id={q.recordId}
                    onclick={toggleSection}>
                    {q.name}
                  </a>
                </div>
                <div class="slds-col slds-size_1-of-12">{q.status}</div>
                <div class="slds-col slds-size_1-of-12">
                  <div class="slds-grid slds-align_absolute-center">
                    <template for:each={q.areasOfNeedImages} for:item="imgUrl">
                      <img
                        key={imgUrl}
                        src={imgUrl}
                        alt="Ambito"
                        style="width:30px; height:30px;" />
                    </template>
                  </div>
                </div>
                <template if:true={isPrevidenza}>
                  <div class="slds-col slds-size_1-of-12">{q.monthlyContribution} €</div>
                </template>
                <template if:false={isPrevidenza}>
                  <div class="slds-col slds-size_1-of-12">{q.totalAmount} €</div>
                </template>
                <div class="slds-col slds-size_1-of-12">{q.digitalStep}</div>
                <div class="slds-col slds-size_1-of-12">{q.source}</div>
                <div class="slds-col slds-size_1-of-12">{q.cip}</div>
                <div class="slds-col slds-size_1-of-12">{q.creationDate}</div>
                <div class="slds-col slds-size_1-of-12">{q.expirationDate}</div>
                <div class="slds-col slds-size_1-of-12">
                  <template if:true={q.unicaLink}>
                    <span class="slds-m-right_x-small">
                      <lightning-icon icon-name="utility:edit" size="small"></lightning-icon>
                    </span>
                    <a role="button" tabindex="0" data-id={q.recordId} onclick={handleManageFEI}>Accedi</a>
                  </template>
                </div>
                <div class="slds-col slds-size_1-of-12">
                  <template if:true={q.documentUrl}>
                    <span class="slds-m-right_x-small">
                      <lightning-icon icon-name="utility:download" size="small"></lightning-icon>
                    </span>
                    <a role="button" tabindex="0" data-id={q.recordId} onclick={myPDFPreview}>PDF</a>
                  </template>
                </div>
              </div>

              <!-- DETTAGLIO ESPANSO -->
              <template if:true={q.isOpen}>
                <!-- dettaglio Previdenza -->
                <template if:true={isPrevidenza}>
                  <div class="slds-grid coverage-data-row slds-text-align_center slds-p-horizontal_none">
                    <div class="slds-col slds-size_1-of-24"></div>
                    <div class="slds-col slds-size_2-of-24"><strong>Prodotto d'interesse</strong></div>
                    <div class="slds-col slds-size_1-of-12"><strong>RAL</strong></div>
                    <div class="slds-col slds-size_1-of-12"><strong>Crescita Retributiva<br/>Annua</strong></div>
                    <div class="slds-col slds-size_1-of-12"><strong>Gap<br/>Previdenziale</strong></div>
                    <div class="slds-col slds-size_1-of-12"><strong>Anno di<br/>Pensionamento</strong></div>
                    <div class="slds-col slds-size_1-of-12"><strong>Numero di<br/>figli</strong></div>
                    <div class="slds-col slds-size_1-of-12"><strong>Settore</strong></div>
                  </div>
                  <template for:each={q.opportunityCoverages} for:item="cov">
                    <div key={cov.key}
                         class="slds-grid coverage-data-row slds-text-align_center slds-p-horizontal_none">
                      <div class="slds-col slds-size_1-of-24"></div>
                      <div class="slds-col slds-size_2-of-24">{cov.targetProduct}</div>
                      <div class="slds-col slds-size_1-of-12">{cov.ral} €</div>
                      <div class="slds-col slds-size_1-of-12">{cov.yearlyGrowth} %</div>
                      <div class="slds-col slds-size_1-of-12">{cov.previdentialGap} €</div>
                      <div class="slds-col slds-size_1-of-12">{cov.retirementYear}</div>
                      <div class="slds-col slds-size_1-of-12">{cov.numOfChildren}</div>
                      <div class="slds-col slds-size_1-of-12">{cov.sector}</div>
                    </div>
                  </template>
                </template>

                <!-- dettaglio Non‑Previdenza -->
                <template if:false={isPrevidenza}>
                  <div class="slds-m-around_x-small">
                    <div class="slds-grid coverage-header-row slds-text-align_center">
                      <div class="slds-col slds-size_1-of-24"></div>
                      <div class="slds-col slds-size_2-of-24"><strong>Ambito</strong></div>
                      <div class="slds-col slds-size_1-of-12"><strong>Bene Assicurato</strong></div>
                      <div class="slds-col slds-size_1-of-12"><strong>Dettaglio</strong></div>
                      <div class="slds-col slds-size_1-of-12"><strong>Premio</strong></div>
                      <div class="slds-col slds-size_1-of-12"><strong>Fonte preventivo</strong></div>
                      <div class="slds-col slds-size_2-of-12"><strong>Frazionamento</strong></div>
                      <div class="slds-col slds-size_1-of-12"><strong>Convenzioni</strong></div>
                      <div class="slds-col slds-size_1-of-12"></div>
                      <div class="slds-col slds-size_1-of-12"></div>
                      <div class="slds-col slds-size_1-of-12"></div>
                    </div>
                    <div class="slds-col slds-size_1-of-1"
                         style="border-bottom:1px solid #d8dde6; margin:-6px 0 8px;"></div>
                    <template for:each={q.opportunityCoverages} for:item="coverage">
                      <div key={coverage.key}
                           class="slds-grid coverage-data-row slds-text-align_center">
                        <div class="slds-col slds-size_1-of-24"></div>
                      <div class="slds-col slds-size_2-of-24 slds-align_absolute-center">
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%;">
                          <img src={coverage.areaIcon}
                              alt={coverage.areaOfNeedText}
                              style="width: 24px; height: 24px;" />
                        </div>
                      </div>
                        <div class="slds-col slds-size_1-of-12 slds-align_absolute-center">
                          <span style="font-size:8pt;">{coverage.assetItems}</span>
                        </div>
                        <div class="slds-col slds-size_1-of-12 slds-align_absolute-center">
                          <span style="font-size:8pt;">{coverage.descriptionItems}</span>
                        </div>
                        <div class="slds-col slds-size_1-of-12 slds-align_absolute-center">
                          <span style="font-size:8pt;">{coverage.amount} €</span>
                        </div>
                        <div class="slds-col slds-size_1-of-12 slds-align_absolute-center">
                          <span style="font-size:8pt;">{coverage.stage}</span>
                        </div>
                        <div class="slds-col slds-size_2-of-12 slds-align_absolute-center">
                          <span style="font-size:8pt;">{coverage.fractionation}</span>
                        </div>
                        <div class="slds-col slds-size_1-of-12 slds-align_absolute-center">
                          <span style="font-size:8pt;">{coverage.conventions}</span>
                        </div>
                        <div class="slds-col slds-size_1-of-12"></div>
                        <div class="slds-col slds-size_1-of-12"></div>
                        <div class="slds-col slds-size_1-of-12"></div>
                      </div>
                    </template>
                  </div>
                </template>
              </template>
            </div>
          </template>
        </div>

        <!-- ========== SEZIONE 2: Altri Preventivi (stored) ========== -->
        <template if:true={hasStoredQuotes}>
          <div class="slds-p-around_small slds-m-top_medium">
          <h3 class="slds-text-heading_small" style="color: #2c7cbc">
            <lightning-button-icon
              variant="bare"
              icon-name={storedSectionIcon}
              alternative-text="Apri/Chiudi"
              onclick={toggleStoredSection}
              class="slds-m-right_x-small"
              style="--slds-c-icon-color-foreground-default: #2c7cbc">
            </lightning-button-icon>
            Altri Preventivi
          </h3>
        </br>
            <div class="slds-m-vertical_x-small "></div>
            <template if:true={storedOpen}>

            <!-- HEADER identico -->
            <div class="slds-grid slds-text-align_center slds-theme_shade header-row">
              <div class="slds-col slds-size_1-of-24"></div>
              <div class="slds-col slds-size_2-of-24"><strong>Id preventivo</strong></div>
              <div class="slds-col slds-size_1-of-12"><strong>Stato</strong></div>
              <div class="slds-col slds-size_2-of-24"><strong>Ambiti</strong></div>
              <template if:true={isPrevidenza}>
                <div class="slds-col slds-size_1-of-12"><strong>Contribuzione mensile</strong></div>
              </template>
              <template if:false={isPrevidenza}>
                <div class="slds-col slds-size_1-of-12"><strong>Premio</strong></div>
              </template>
              <div class="slds-col slds-size_1-of-12"><strong>Step Digitale</strong></div>
              <div class="slds-col slds-size_1-of-12"><strong>Fonte preventivo</strong></div>
              <div class="slds-col slds-size_1-of-12"><strong>CIP</strong></div>
              <div class="slds-col slds-size_1-of-12"><strong>Data Creazione</strong></div>
              <div class="slds-col slds-size_1-of-12"><strong>Data di Scadenza</strong></div>
              <div class="slds-col slds-size_1-of-12"><strong>{unicaLabel}</strong></div>
              <div class="slds-col slds-size_1-of-12"><strong>Visualizza PDF</strong></div>
            </div>
<hr class="slds-m-vertical_x-small slds-theme_shade"/>

            <!-- RIGHE storedQuotes -->
              <template for:each={storedQuotes} for:item="q">
                <div key={q.recordId} class="quote-container slds-m-top_x-small slds-theme_shade">
                  <!-- RIGA PRINCIPALE -->
                  <div class="slds-grid slds-text-align_center data-row ">
                    <div class="slds-col slds-size_1-of-24">
                      <template if:true={q.isOpen}>
                        <lightning-button-icon
                          variant="bare"
                          icon-name="utility:chevrondown"
                          alternative-text="Chiudi"
                          data-id={q.recordId}
                          onclick={toggleSection}>
                        </lightning-button-icon>
                      </template>
                      <template if:false={q.isOpen}>
                        <lightning-button-icon
                          variant="bare"
                          icon-name="utility:chevronright"
                          alternative-text="Apri"
                          data-id={q.recordId}
                          onclick={toggleSection}>
                        </lightning-button-icon>
                      </template>
                    </div>
                    <div class="slds-col slds-size_2-of-24">
                      <a data-id={q.recordId} onclick={handleQuoteClick}>{q.name}</a>
                    </div>
                    <div class="slds-col slds-size_1-of-12">{q.status}</div>
                    <div class="slds-col slds-size_2-of-24">
                      <div class="slds-grid slds-align_absolute-center">
                        <template for:each={q.areasOfNeedImages} for:item="imgUrl">
                          <img key={imgUrl} src={imgUrl} alt="Ambito" style="width:30px; height:30px;" />
                        </template>
                      </div>
                    </div>
                    <template if:true={isPrevidenza}>
                      <div class="slds-col slds-size_1-of-12">{q.monthlyContribution} €</div>
                    </template>
                    <template if:false={isPrevidenza}>
                      <div class="slds-col slds-size_1-of-12">{q.totalAmount} €</div>
                    </template>
                    <div class="slds-col slds-size_1-of-12">{q.digitalStep}</div>
                    <div class="slds-col slds-size_1-of-12">{q.source}</div>
                    <div class="slds-col slds-size_1-of-12">{q.cip}</div>
                    <div class="slds-col slds-size_1-of-12">{q.creationDate}</div>
                    <div class="slds-col slds-size_1-of-12">{q.expirationDate}</div>
                    <div class="slds-col slds-size_1-of-12">
                      <template if:true={q.unicaLink}>
                        <span class="slds-m-right_x-small">
                          <lightning-icon icon-name="utility:edit" size="small"></lightning-icon>
                        </span>
                        <a role="button" tabindex="0" data-id={q.recordId} onclick={handleManageFEI}>Accedi</a>
                      </template>
                    </div>
                    <div class="slds-col slds-size_1-of-12">
                      <template if:true={q.documentUrl}>
                        <span class="slds-m-right_x-small">
                          <lightning-icon icon-name="utility:download" size="small"></lightning-icon>
                        </span>
                        <a role="button" data-id={q.recordId} onclick={myPDFPreview}>PDF</a>
                      </template>
                    </div>
                  </div>

                  <!-- DETTAGLIO ESPANSO stored -->
                  <template if:true={q.isOpen}>
                    <!-- dettaglio Previdenza -->
                    <template if:true={isPrevidenza}>
                      <div class="slds-grid coverage-data-row slds-text-align_center slds-p-horizontal_none">
                        <div class="slds-col slds-size_1-of-24"></div>
                        <div class="slds-col slds-size_2-of-24"><strong>Prodotto d'interesse</strong></div>
                        <div class="slds-col slds-size_1-of-12"><strong>RAL</strong></div>
                        <div class="slds-col slds-size_1-of-12"><strong>Crescita Retributiva<br/>Annua</strong></div>
                        <div class="slds-col slds-size_1-of-12"><strong>Gap<br/>Previdenziale</strong></div>
                        <div class="slds-col slds-size_1-of-12"><strong>Anno di<br/>Pensionamento</strong></div>
                        <div class="slds-col slds-size_1-of-12"><strong>Numero di<br/>figli</strong></div>
                        <div class="slds-col slds-size_1-of-12"><strong>Settore</strong></div>
                      </div>
                      <template for:each={q.opportunityCoverages} for:item="cov">
                        <div key={cov.key}
                             class="slds-grid coverage-data-row slds-text-align_center slds-p-horizontal_none">
                          <div class="slds-col slds-size_1-of-24"></div>
                          <div class="slds-col slds-size_2-of-24">{cov.targetProduct}</div>
                          <div class="slds-col slds-size_1-of-12">{cov.ral} €</div>
                          <div class="slds-col slds-size_1-of-12">{cov.yearlyGrowth} %</div>
                          <div class="slds-col slds-size_1-of-12">{cov.previdentialGap} €</div>
                          <div class="slds-col slds-size_1-of-12">{cov.retirementYear}</div>
                          <div class="slds-col slds-size_1-of-12">{cov.numOfChildren}</div>
                          <div class="slds-col slds-size_1-of-12">{cov.sector}</div>
                        </div>
                      </template>
                    </template>

                    <!-- dettaglio Non‑Previdenza -->
                    <template if:false={isPrevidenza}>
                      <div class="slds-m-around_x-small">
                        <div class="slds-grid coverage-header-row slds-text-align_center">
                          <div class="slds-col slds-size_1-of-24"></div>
                          <div class="slds-col slds-size_2-of-24"><strong>Ambito</strong></div>
                          <div class="slds-col slds-size_1-of-12"><strong>Bene assicurato</strong></div>
                          <div class="slds-col slds-size_1-of-12"><strong>Dettaglio</strong></div>
                          <div class="slds-col slds-size_1-of-12"><strong>Premio</strong></div>
                          <div class="slds-col slds-size_1-of-12"><strong>Fonte Preventivo</strong></div>
                          <div class="slds-col slds-size_2-of-12"><strong>Frazionamento</strong></div>
                          <div class="slds-col slds-size_1-of-12"><strong>Convenzioni</strong></div>
                          <div class="slds-col slds-size_1-of-12"></div>
                          <div class="slds-col slds-size_1-of-12"></div>
                          <div class="slds-col slds-size_1-of-12"></div>
                        </div>
                        <div class="slds-col slds-size_1-of-1"
                             style="border-bottom:1px solid #d8dde6; margin:-6px 0 8px;"></div>
                        <template for:each={q.opportunityCoverages} for:item="coverage">
                          <div key={coverage.key}
                               class="slds-grid coverage-data-row slds-text-align_center">
                            <div class="slds-col slds-size_1-of-24"></div>
                          <div class="slds-col slds-size_2-of-24 slds-align_absolute-center">
                            <div style="display: flex; align-items: center; justify-content: center; height: 100%;">
                              <img src={coverage.areaIcon}
                                  alt={coverage.areaOfNeedText}
                                  style="width: 24px; height: 24px;" />
                            </div>
                          </div>
                            <div class="slds-col slds-size_1-of-12 slds-align_absolute-center">
                              <span style="font-size:8pt;">{coverage.assetItems}</span>
                            </div>
                            <div class="slds-col slds-size_1-of-12 slds-align_absolute-center">
                              <span style="font-size:8pt;">{coverage.descriptionItems}</span>
                            </div>
                            <div class="slds-col slds-size_1-of-12 slds-align_absolute-center">
                              <span style="font-size:8pt;">{coverage.amount} €</span>
                            </div>
                            <div class="slds-col slds-size_1-of-12 slds-align_absolute-center">
                              <span style="font-size:8pt;">{coverage.stage}</span>
                            </div>
                            <div class="slds-col slds-size_2-of-12 slds-align_absolute-center">
                              <span style="font-size:8pt;">{coverage.fractionation}</span>
                            </div>
                            <div class="slds-col slds-size_1-of-12 slds-align_absolute-center">
                              <span style="font-size:8pt;">{coverage.conventions}</span>
                            </div>
                            <div class="slds-col slds-size_1-of-12"></div>
                            <div class="slds-col slds-size_1-of-12"></div>
                            <div class="slds-col slds-size_1-of-12"></div>
                          </div>
                        </template>
                      </div>
                    </template>
                  </template>
                </div>
              </template>
            </template>
          </div>
        </template>
      </template>
    </template>
  </lightning-card>

  <!-- Modale PDF -->
  <c-pdf-view-modal
    url={currentPreviewUrl}
    domain-type={currentPreviewDomain}
    show-modal={showPreview}
    onclose={closePDFPreview}>
  </c-pdf-view-modal>

  <!-- Modale Flow FEI -->
  <template if:true={showFEI}>
    <c-flow-container-modal
      flow-name={flowName}
      title={flowTitle}
      show-modal={showFEI}
      flow-variables={flowInputVariables}
      onclose={handleClose}>
    </c-flow-container-modal>
  </template>
</template>