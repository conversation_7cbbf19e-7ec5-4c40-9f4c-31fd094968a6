public class UsersUcaWrapper {

    @AuraEnabled
    public List<UserUcaWrapper> users;

    public class UserUcaWrapper {
        @AuraEnabled
        public String userId;

        @AuraEnabled
        public List<String> groups;

        @AuraEnabled
        public List<String> cin;
    }

    public static UsersUcaWrapper fromJson(String jsonString) {
        if(jsonString == null || String.isBlank(jsonString)) {
            return null;
        }
        
        return (UsersUcaWrapper) JSON.deserialize(jsonString, UsersUcaWrapper.class);
    }

    public String toJson() {
        return JSON.serialize(this);
    }
}