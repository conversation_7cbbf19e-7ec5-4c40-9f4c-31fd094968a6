<template>
    <div class="container-vertical">
        <template if:true={displayHeaderTitle}>
            <div class="container-title-horizontal">
                <div class="buttons-container" style="width: 40px;"><!--// SC 09-04-25 OCT 1289109-->
                </div>
                <!-- Title Goes Here -->
                <template if:true={isPrevidenza}>
                    <div class="item-vertical" style="width: 175px;"><!--// SC 09-04-25 OCT 1289109-->
                        <p class="item-title">Id preventivo</p>
                    </div>
                    <div class="item-vertical" style="width: 100px;">
                        <p class="item-title">Ambito</p>
                    </div>
                    <div class="item-vertical" style="width: 200px;">
                        <p class="item-title">Contribuzione mensile</p>
                    </div>
                    <div class="item-vertical" style="width: 200px;">
                        <p class="item-title">Punto di ingaggio</p>
                    </div>
                    <div class="item-vertical" style="width: 200px;">
                        <p class="item-title">Data Creazione</p>
                    </div>
                    <div class="item-vertical" style="width: 200px;">
                        <p class="item-title">Data di Scadenza</p>
                    </div>
                    <div class="item-vertical" style="width: 150px;">
                        <p class="item-title">Scarica</p>
                    </div>
                </template>
                <template if:false={isPrevidenza}>
                    <div class="data-container">
                        <div class="item-vertical" style="width: 200px;"><!--// SC 09-04-25 OCT 1289109-->
                            <p class="item-title">Id preventivo</p>
                        </div><!--// SC 09-04-25 OCT 1289109-->
                        <div class="item-vertical" style="width: 90px;">
                            <p class="item-title">Stato</p>
                        </div><!--// SC 09-04-25 OCT 1289109-->
                        <div class="item-vertical" style="width: 165px;">
                            <p class="item-title">Ambiti</p>
                        </div>
                        <div class="item-vertical" style="width: 100px;">
                            <p class="item-title">Premio</p>
                        </div><!--// SC 09-04-25 OCT 1289109-->
                        <div class="item-vertical" style="width: 180px;">
                            <p class="item-title">Step Digitale</p>
                        </div><!--// SC 09-04-25 OCT 1289109-->
                        <div class="item-vertical" style="width: 200px;">
                            <p class="item-title">Fonte preventivo</p><!--// SC 09-04-25 OCT 1289109-->
                        </div><!--// SC 09-04-25 OCT 1289109-->
                        <div class="item-vertical" style="width: 100px;">
                            <p class="item-title">CIP</p>
                        </div><!--// SC 09-04-25 OCT 1289109-->
                        <div class="item-vertical" style="width: 185px;">
                            <p class="item-title">Data Creazione</p>
                        </div>
                        <div class="item-vertical" style="width: 200px;">
                            <p class="item-title">Data di Scadenza</p>
                        </div>
                        <div class="item-vertical" style="width: 150px;">
                            <p class="item-title">{unicaLabel}</p>
                        </div>
                        <div class="item-vertical" style="width: 150px;">
                            <p class="item-title">Visualizza PDF</p>
                        </div>
                    </div>
                </template>
            </div>
        </template>
        <div class="container-data-horizontal">
            <div class="button-container">
                <div class="item-vertical" style="width: 30px;"><!--// SC 09-04-25 OCT 1289109-->
                    <div class="item-body" style="justify-content: center;">
                        <lightning-button-icon 
                            icon-name={buttonIcon} 
                            variant="container"
                            onclick={toggleContent}>
                        </lightning-button-icon>
                    </div>
                </div>
            </div>
            <!-- Data Goes Here -->
            <template if:true={isPrevidenza}>
                <div class="data-container">
                    <div class="item-vertical" style="width: 175px;"><!--// SC 09-04-25 OCT 1289109-->
                        <a onclick={toggleContent} class="item-body"> {name} </a>
                    </div>
                    <div class="item-vertical" style="width: 150px;">
                        <div class="item-body"> 
                            <template for:each={areasOfNeedImages} for:item="image">
                                <image style="margin-right: 3px;" key={image} width=20 height=20 src={image} />
                            </template>
                        </div>
                    </div>
                    <div class="item-vertical" style="width: 150px;">
                        <div class="item-body"> {monthlyContribution} €</div>
                    </div>
                    <div class="item-vertical" style="width: 200px;">
                        <div class="item-body"> {source} </div>
                    </div>
                    <div class="item-vertical" style="width: 200px;">
                        <div class="item-body"> {creationDate} </div>
                    </div>
                    <div class="item-vertical" style="width: 200px;">
                        <div class="item-body"> {expirationDate} </div>
                    </div>
                    <div class="item-vertical" style="width: 150px;">
                        <div class="item-body">
                            <template if:false={isOpportunityClosed}>
                                <template if:true={hasDocument}>
                                    <span>
                                        <lightning-icon icon-name="utility:download" size="small"></lightning-icon>
                                    </span>
                                    <span>
                                        <a style="margin-left:10px;text-wrap:balance;text-align:center;" href="#" onclick={openPDFPreview}>Simulazione</a>
                                    </span>
                                </template>
                                <template if:false={hasDocument}>
                                    <span>Non disponibile</span>
                                </template>
                            </template>
                            <template if:true={isOpportunityClosed}>
                                <span style="text-align:center; color:darkred; "></span>
                            </template>
                        </div>
                    </div>
                </div>
            </template>
            <template if:false={isPrevidenza}>
                <div class="data-container">
                    <div class="item-vertical" style="width: 190px;"><!--// SC 09-04-25 OCT 1289109-->
                        <a onclick={toggleContent} class="item-body"> {name}</a>
                    </div><!--// SC 09-04-25 OCT 1289109-->
                    <div class="item-vertical" style="width: 100px;">
                        <div class="item-body"> {status} </div>
                    </div><!--// SC 09-04-25 OCT 1289109-->
                    <div class="item-vertical" style="width: 200px;">
                        <div class="item-body"> 
                            <template for:each={areasOfNeedImages} for:item="image">
                                <image style="margin-right: 3px;" key={image} width=20 height=20 src={image} />
                            </template>
                        </div>
                    </div>
                    <div class="item-vertical" style="width: 100px;">
                        <div class="item-body"> {totalAmount} €</div>
                    </div><!--// SC 09-04-25 OCT 1289109-->
                    <div class="item-vertical" style="width: 210px;">
                        <div class="item-body"> {digitalStep} </div>
                    </div><!--// SC 09-04-25 OCT 1289109-->
                    <div class="item-vertical" style="width: 200px;">
                        <div class="item-body"> {source} </div><!--// SC 09-04-25 OCT 1289109-->
                    </div>
                    <div class="item-vertical" style="width: 100px;">
                        <div class="item-body"> {cip} </div><!--// SC 09-04-25 OCT 1289109-->
                    </div>
                    <div class="item-vertical" style="width: 200px;">
                        <div class="item-body"> {creationDate} </div>
                    </div>
                    <div class="item-vertical" style="width: 150px;">
                        <div class="item-body"> {expirationDate} </div>
                    </div>
                    <div class="item-vertical" style="width: 200px;">
                        <div class="item-body">
                            <template if:true={showAccediLink}> <!--SC: 26-05-25-->
                                <span style="margin-left: 25px;">
                                    <lightning-icon icon-name="utility:edit" size="small"></lightning-icon>
                                </span>
                                <span style="text-align:center">
                                    <a style="text-wrap:wrap;margin-left: 4px;" href="#" onclick={handleManageFEI}>Accedi</a>
                                    <!--<span style="margin-left: 3px;margin-right: 3px;">|</span>
                                    <a style="text-wrap:wrap;margin-right: 9px;" href="#" onclick={handleModificaClick}>Modifica</a>-->
                                </span>                            
                            </template>
                            <template if:false={showAccediLink}><!--SC: 26-05-25-->
                                <span style="text-align:center; color:darkred; "></span>
                            </template>
                        </div>
                    </div>
                    <div class="item-vertical" style="width: 150px;">
                        <div class="item-body">
                            <template if:true={showPdfLink}><!--SC: 26-05-25-->
                                <span>
                                    <lightning-icon icon-name="utility:download" size="small"></lightning-icon>
                                </span>
                                <span>
                                    <a style="margin-left:10px;text-wrap:balance;text-align:center;" href="#" onclick={myPDFPreview}>PDF</a>
                                </span>
                            </template>
                            <template if:false={hideQuoteUtilityPDF}><!--SC: 26-05-25-->
                                <span style="text-align:center; color:darkred; "></span>
                            </template>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
    <div if:true={showContent}>
        <template for:each={opportunityCoverages} for:item="oc">
            <c-accordion-details key={oc.Id}
                opportunity-coverage={oc}
                source={sourceC}>
            </c-accordion-details>
        </template>
    </div>
    <c-pdf-view-modal url={documentUrl} domain-type={domainType} show-modal={showPreview} onclose={closePDFPreview}></c-pdf-view-modal>
    <!-- <c-flow-container-modal flow-name={flowName} title={flowTitle} show-modal={showFEI} flow-variables={flowInputVariables} onclose={handleClose}></c-flow-container-modal> -->

<template if:true={showFEI}>
    <c-flow-container-modal flow-name={flowName} title={flowTitle} show-modal={showFEI} flow-variables={flowInputVariables} onclose={handleClose}></c-flow-container-modal>
</template>
</template>