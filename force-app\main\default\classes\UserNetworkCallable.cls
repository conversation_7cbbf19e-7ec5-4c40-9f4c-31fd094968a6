global class UserNetworkCallable implements System.Callable {

    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');

        Object result = invokeMethod(action, input, output, options);
        return result;
    }

    global Boolean invokeMethod(String methodName, Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        Boolean result = true;

        try {
            if (methodName == 'getUserContext') {
                getUserContext(methodName, inputMap, outMap, options);
            }
        } catch (Exception e) {
            outMap.put('success', false);
            outMap.put('errorType', 0);
            outMap.put('message', 'Errore generico: ' + e.getMessage());
            result = false;
        }

        return result;
    }

    global void getUserContext(String methodName, Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        // Implement the logic to get user context
        outMap.put('success', true);
        outMap.put('data', JSON.serialize(UserUtils.getUserContext()));
    }

}