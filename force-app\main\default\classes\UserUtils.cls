public without sharing class UserUtils {
  public static Map<String, String> societyCodeMap = new Map<String, String>{
      'SOC_1' => 'UNIPOL',
      'SOC_4' => 'UNISALUTE'
  };

  public static Map<String, String> societyMap {
      get {
          Map<String, String> reverseMap = new Map<String, String>();
          for (String key : societyCodeMap.keySet()) {
              reverseMap.put(societyCodeMap.get(key), key);
          }
          return reverseMap;
      }
  }

  private static UserContext privateCurrentUserContext;
  public final static UserContext currentUserContext {
    get {
      if (UserUtils.privateCurrentUserContext == null) {
        UserUtils.UserInput input = new UserUtils.UserInput();
        input.userId = UserInfo.getUserId();
        UserUtils.privateCurrentUserContext = new UserContext(
          input
        );
      }
      return UserUtils.privateCurrentUserContext;
    }
  }

  public class UserInput{
    @AuraEnabled
    public String userId;

    @AuraEnabled
    public String fiscalCode;

    @AuraEnabled
    public String userCode;

    @AuraEnabled
    public Boolean useCurrentUser;

    @AuraEnabled
    public Boolean findAgencyUserNetworks;

    @AuraEnabled
    public Boolean findPermissionSets;
  }

  public class UserContext {
    @AuraEnabled
    public User user;

    @AuraEnabled
    public String userId;

    @AuraEnabled
    public String profileName;

    @AuraEnabled
    public Map<String, UserNetwork> userNetworks;

    @AuraEnabled
    public UsersUcaWrapper ucaUsers;

    @AuraEnabled
    public String societyPreferedCode = 'SOC_1'; // Default to UNIPOL

    @AuraEnabled
    public UserNetwork societyPrefered;

    @AuraEnabled
    public List<String> permissionSets;

    @AuraEnabled
    public UserInput input;

    /**
     * Costruttore per la classe `UserContext`.
     *
     * @param userId Id dell'utente per cui costruire il contesto.
     * @param input Input opzionale per specificare codice fiscale o codice utente.
     */
    public UserContext(UserInput input) {
      if (input == null) {
        throw new AuraHandledException('UserInput cannot be null.');
      }

      this.input = input;

      if(this.input.useCurrentUser != null && this.input.useCurrentUser) {
        this.input.userId = UserInfo.getUserId();
      }

      if (input.userId != null) {
        this.user = UserUtils.getUser(input.userId);
      } else if (input.fiscalCode != null) {
        this.user = UserUtils.getUserByFiscalCode(input.fiscalCode);
      } else if (input.userCode != null) {
        this.user = UserUtils.getUserByUserCode(input.userCode);
      }
    

      if (this.user == null) {
        throw new AuraHandledException('User not found for the provided ID or fiscalCode or userCode.');
      }

      this.input = input;
      this.userId = this.user.Id;
      this.ucaUsers = UsersUcaWrapper.fromJson(user.UCA_Permissions__c);
      this.profileName = user.Profile.Name;

      this.generateNetworkUser(this.user);
      this.getPermissionSets(input);
    }

    /**
     * Popola le informazioni di rete per l'utente e determina la società preferita.
     * Analizza i record `NetworkUser__c` legati al codice fiscale,
     * li organizza in una mappa per società e individua quella da usare
     * come predefinita.
     *
     * @param user Oggetto utente da cui recuperare i dati di identificazione.
     */
    private void generateNetworkUser(User user) {
      List<NetworkUser__c> networks = UserUtils.getUserNetworksByFederationId(
        this.user.FederationIdentifier
      );
      if (networks == null || networks.isEmpty()) {
        return;
      }
      this.userNetworks = new Map<String, UserNetwork>();
      this.processNetworks(networks);
      this.assignPreferredNetworks();

      List<String> societyCodes = new List<String>(this.userNetworks.keySet());
      if (societyCodes.size() == 1) {
        this.societyPreferedCode = societyCodes[0];
      }

      this.societyPrefered = this.userNetworks.get(this.societyPreferedCode);
    }

    private void getPermissionSets(UserInput input) {
      if(input == null || input.findPermissionSets == null || !input.findPermissionSets) {
        return;
      }

      this.permissionSets = UserUtils.getUserPermissionSets(this.userId);
    }

    /**
     * Costruisce la mappa delle reti dell'utente e applica eventuali preferenze.
     * Per ogni record `NetworkUser__c` crea o recupera il relativo
     * `UserNetwork`, aggiunge la rete alla lista e gestisce il flag di
     * preferenza.
     *
     * @param networks Elenco di reti attive dell'utente.
     */
    private void processNetworks(List<NetworkUser__c> networks) {
      for (NetworkUser__c network : networks) {
        if (network.Society__c == null || network.NetworkUser__c == null) {
          continue;
        }
        UserNetwork userNetwork = this.getOrCreateUserNetwork(
          network.Society__c
        );
        userNetwork.networks.add(network);
        if (network.Preferred__c) {
          this.applyPreferredNetwork(userNetwork, network);
        }
        this.userNetworks.put(network.Society__c, userNetwork);
      }
    }

    /**
     * Restituisce l'oggetto `UserNetwork` per il codice società indicato
     * oppure ne crea uno nuovo popolando le informazioni di base.
     *
     * @param societyCode Codice della società (es. `SOC_1`).
     * @return Istanza esistente o nuova di `UserNetwork`.
     */
    private UserNetwork getOrCreateUserNetwork(String societyCode) {
      if (this.userNetworks.containsKey(societyCode)) {
        return this.userNetworks.get(societyCode);
      }

      String society = societyCodeMap.get(societyCode);

      UserNetwork userNetwork = new UserNetwork();
      userNetwork.society = society;
      userNetwork.societyCode = societyCode;
      userNetwork.societyId = societyCode.replace('SOC_', '');
      userNetwork.networks = new List<NetworkUser__c>();
      return userNetwork;
    }

    /**
     * Garantisce che ogni società abbia una rete preferita.
     * Se nessuna rete è marcata come tale, viene scelto il primo
     * elemento disponibile.
     */
    private void assignPreferredNetworks() {
      for (UserNetwork userNetwork : this.userNetworks.values()) {
        if (userNetwork.preferredNetwork == null) {
          this.applyPreferredNetwork(userNetwork, null);
        }
      }
    }

    /**
     * Applica la rete preferita e arricchisce i dati di utenza e agenzia.
     * Quando una rete viene selezionata come preferita:
     * - imposta l'identificativo dell'utente di rete;
     * - associa i dettagli UCA corrispondenti;
     * - costruisce il wrapper dell'agenzia relativa.
     *
     * @param userNetwork Contenitore della società a cui applicare la preferenza.
     * @param preferredNetwork Record da utilizzare come preferito, se già noto.
     */
    private void applyPreferredNetwork(
      UserNetwork userNetwork,
      NetworkUser__c preferredNetwork
    ) {
      if (
        userNetwork.preferredNetwork == null &&
        userNetwork.networks.size() > 0
      ) {
        userNetwork.preferredNetwork = preferredNetwork != null
          ? preferredNetwork
          : userNetwork.networks[0];
        userNetwork.userId = userNetwork.preferredNetwork.ExternalId__c;
        userNetwork.ucaUser = this.getUcaUser(userNetwork.userId);
        userNetwork.agency = this.getUserAgency(userNetwork);
      }
    }

    /**
     * Recupera l'agenzia associata alla rete preferita dell'utente.
     * Sfrutta la relazione `Agency__r` del record `NetworkUser__c`
     * per costruire un wrapper con codice e account.
     *
     * @param userNetwork Contenitore da cui estrarre l'agenzia preferita.
     * @return Wrapper con informazioni dell'agenzia o `null` se assente.
     */
    private UserAgency getUserAgency(UserNetwork userNetwork) {
      if (
        userNetwork.preferredNetwork == null ||
        userNetwork.preferredNetwork.Agency__c == null
      ) {
        return null;
      }
      UserAgency userAgency = new UserAgency(this.input);
      userAgency.code = userNetwork.preferredNetwork.Agency__r.AgencyCode__c;
      userAgency.agency = userNetwork.preferredNetwork.Agency__r;
      return userAgency;
    }

    /**
     * Restituisce i dettagli UCA relativi all'utente specificato.
     * Scorre la lista deserializzata dei permessi UCA e cerca
     * l'entry con identificativo corrispondente.
     *
     * @param userId Identificativo esterno dell'utente di rete.
     * @return Istanza `UserUcaWrapper` oppure `null` se non trovata.
     */
    private UsersUcaWrapper.UserUcaWrapper getUcaUser(String userId) {
      for (UsersUcaWrapper.UserUcaWrapper ucaUser : this.ucaUsers.users) {
        if (ucaUser.userId.equals(userId)) {
          return ucaUser;
        }
      }
      return null;
    }
  }

  public class UserNetwork {
    @AuraEnabled
    public String society;

    @AuraEnabled
    public String societyCode;

    @AuraEnabled
    public String societyId;

    @AuraEnabled
    public String userId;

    @AuraEnabled
    public UserAgency agency;

    @AuraEnabled
    public UsersUcaWrapper.UserUcaWrapper ucaUser;

    @AuraEnabled
    public List<NetworkUser__c> networks = new List<NetworkUser__c>();

    @AuraEnabled
    public NetworkUser__c preferredNetwork;

    /**
     * Indica se la rete è contrassegnata come preferita.
     *
     * @return `true` se esiste un `preferredNetwork`, altrimenti `false`.
     */
    public Boolean isPreferred() {
      return this.preferredNetwork != null;
    }

    /**
     * Verifica l'appartenenza dell'utente al gruppo indicato.
     *
     * @param groupName Nome del gruppo da verificare.
     * @return `true` se il gruppo è presente nella lista UCA, `false` in caso contrario.
     */
    public Boolean checkGroup(String groupName) {
      if (this.ucaUser == null || this.ucaUser.groups == null) {
        return false;
      }
      return this.ucaUser.groups.contains(groupName);
    }

    /**
     * Controlla se l'utente appartiene ad almeno uno dei gruppi forniti.
     *
     * @param groupNames Elenco di gruppi possibili.
     * @return `true` se l'utente è in almeno un gruppo, altrimenti `false`.
     */
    public Boolean checkAnyGroup(List<String> groupNames) {
      if (this.ucaUser == null || this.ucaUser.groups == null) {
        return false;
      }
      for (String groupName : groupNames) {
        if (this.ucaUser.groups.contains(groupName)) {
          return true;
        }
      }
      return false;
    }

    /**
     * Verifica che l'utente appartenga a tutti i gruppi specificati.
     *
     * @param groupNames Elenco di gruppi richiesti.
     * @return `true` se l'utente è presente in tutti i gruppi indicati.
     */
    public Boolean checkAllGroups(List<String> groupNames) {
      if (this.ucaUser == null || this.ucaUser.groups == null) {
        return false;
      }
      for (String groupName : groupNames) {
        if (!this.ucaUser.groups.contains(groupName)) {
          return false;
        }
      }
      return true;
    }
  }

  public class UserAgency {
    @AuraEnabled
    public String code;

    @AuraEnabled
    public Account agency;

    @AuraEnabled
    public List<UserNetwork> userNetworks;

    /**
     * Costruttore per la classe `UserAgency`.
     * Inizializza la lista di reti associate all'agenzia.
     * Se `findAgencyUserNetworks` è `true`, chiama il metodo `findUserNetworks()`
     * per popolare le reti attive dell'agenzia corrente
     */
    public UserAgency(UserInput input) {
      if( input != null && input.findAgencyUserNetworks != null && input.findAgencyUserNetworks) {
        this.findUserNetworks();
      }
    }

    /**
     * Popola le reti associate all'agenzia corrente.
     * Recupera i record `NetworkUser__c` attivi per l'agenzia e crea
     * un oggetto `UserNetwork` minimale per ciascuno, marcandolo come preferito.
     */
    public void findUserNetworks() {
      if (this.agency == null || this.agency.Id == null) {
        return;
      }
      
      List<NetworkUser__c> userNetworks = UserUtils.getUserNetworksByAgency(
        this.agency.Id
      );
      if (userNetworks == null || userNetworks.isEmpty()) {
        return;
      }
      this.userNetworks = new List<UserNetwork>();
      for (NetworkUser__c network : userNetworks) {
        if (network.Society__c == null || network.NetworkUser__c == null) {
          continue;
        }

        UserNetwork userNetwork = new UserNetwork();
        userNetwork.society = network.Society__c;
        userNetwork.societyCode = network.Society__c;
        userNetwork.societyId = network.Society__c.replace('SOC_', '');
        userNetwork.userId = network.ExternalId__c;
        userNetwork.agency = this;
        userNetwork.networks = new List<NetworkUser__c>{ network };
        userNetwork.preferredNetwork = network;
        this.userNetworks.add(userNetwork);
      }
    }
  }

  /**
   * Restituisce l'utente con i campi necessari partendo dall'ID.
   * Esegue un controllo di accesso sull'oggetto `User` prima della query.
   *
   * @param userId Identificativo Salesforce dell'utente.
   * @return Record `User` con i campi essenziali per costruire il contesto.
   */
  @AuraEnabled
  public static User getUser(String userId) {
    if (!Schema.sObjectType.User.isAccessible()) {
      throw new SecurityException(
        'Insufficient permissions to access User object.'
      );
    }

    return [
      SELECT Id, Name, Email, Username, Personas__c, Societes__c, FederationIdentifier, UCA_Permissions__c, Profile.Name, UserRole.Name
      FROM User
      WHERE Id = :userId
      LIMIT 1
    ];
  }

  /**
   * Restituisce l'utente con i campi necessari partendo dal codice fiscale.
   * Esegue un controllo di accesso sull'oggetto `User` prima della query.
   *
   * @param fiscalCode Codice fiscale dell'utente.
   * @return Record `User` con i campi essenziali per costruire il contesto.
   */
  @AuraEnabled
  public static User getUserByFiscalCode(String fiscalCode) {
    if (!Schema.sObjectType.User.isAccessible()) {
      throw new SecurityException(
        'Insufficient permissions to access User object.'
      );
    }

    return [
      SELECT Id, Name, Email, Username, Personas__c, Societes__c, FederationIdentifier, UCA_Permissions__c, Profile.Name, UserRole.Name
      FROM User
      WHERE FederationIdentifier = :fiscalCode
      LIMIT 1
    ];
  }

  /**
   * Crea un nuovo contesto per l'utente basato sul codice utente.
   *
   * @param userCode Codice utente dell'utente di cui costruire il contesto.
   * @return Istanza appena creata di `UserContext`.
   */
  @AuraEnabled
  public static User getUserByUserCode(String userCode) {
    if (!Schema.sObjectType.User.isAccessible()) {
      throw new SecurityException(
        'Insufficient permissions to access User object.'
      );
    }

    List<NetworkUser__c> networkUsers = [
      SELECT Id, FiscalCode__c
      FROM NetworkUser__c
      WHERE ExternalId__c = :userCode
      AND IsActive__c = TRUE
      LIMIT 1
    ];

    if (networkUsers.isEmpty()) {
      throw new AuraHandledException(
        'No active NetworkUser found for the provided user code.'
      );
    }

    NetworkUser__c networkUser = networkUsers[0];

    return UserUtils.getUserByFiscalCode(networkUser.FiscalCode__c);
  }

  /**
   * Ottiene le reti attive dell'utente in base al codice fiscale (FederationId).
   * Verifica i permessi sullo sObject e restituisce solo record attivi.
   *
   * @param federationIdentifier Codice fiscale usato come chiave di ricerca.
   * @return Lista di reti `NetworkUser__c` attive.
   */
  @AuraEnabled
  public static List<NetworkUser__c> getUserNetworksByFederationId(
    String federationIdentifier
  ) {
    if (!Schema.sObjectType.NetworkUser__c.isAccessible()) {
      throw new SecurityException(
        'Insufficient permissions to access NetworkUser__c object.'
      );
    }

    return [
      SELECT
        Id,
        OwnerId,
        IsDeleted,
        Name,
        CreatedDate,
        CreatedById,
        LastModifiedDate,
        LastModifiedById,
        SystemModstamp,
        LastActivityDate,
        Agency__c,
        ExternalId__c,
        FiscalCode__c,
        IsActive__c,
        NetworkUser__c,
        PermissionSets__c,
        Profile__c,
        Role__c,
        Society__c,
        User__c,
        Preferred__c,
        Agency__r.Name,
        Agency__r.ExternalId__c,
        Agency__r.AgencyCode__c
      FROM NetworkUser__c
      WHERE FiscalCode__c = :federationIdentifier AND IsActive__c = TRUE
    ];
  }

  /**
   * Recupera le reti attive associate all'agenzia indicata.
   *
   * @param agencyId Id Salesforce dell'agenzia.
   * @return Elenco di reti `NetworkUser__c` attive per l'agenzia.
   */
  @AuraEnabled
  public static List<NetworkUser__c> getUserNetworksByAgency(String agencyId) {
    if (!Schema.sObjectType.NetworkUser__c.isAccessible()) {
      throw new SecurityException(
        'Insufficient permissions to access NetworkUser__c object.'
      );
    }

    return [
      SELECT
        Id,
        OwnerId,
        IsDeleted,
        Name,
        CreatedDate,
        CreatedById,
        LastModifiedDate,
        LastModifiedById,
        SystemModstamp,
        LastActivityDate,
        Agency__c,
        ExternalId__c,
        FiscalCode__c,
        IsActive__c,
        NetworkUser__c,
        PermissionSets__c,
        Profile__c,
        Role__c,
        Society__c,
        User__c,
        Preferred__c,
        Agency__r.Name,
        Agency__r.ExternalId__c,
        Agency__r.AgencyCode__c
      FROM NetworkUser__c
      WHERE Agency__c = :agencyId AND IsActive__c = TRUE
    ];
  }

  public static List<String> getUserPermissionSets(String userId) {
    if (!Schema.sObjectType.PermissionSetAssignment.isAccessible()) {
      throw new SecurityException(
        'Insufficient permissions to access PermissionSetAssignment object.'
      );
    }

    List<PermissionSetAssignment> assignments = [
      SELECT PermissionSet.Name
      FROM PermissionSetAssignment
      WHERE AssigneeId = :userId
    ];

    List<String> permissionSetNames = new List<String>();
    for (PermissionSetAssignment assignment : assignments) {
      permissionSetNames.add(assignment.PermissionSet.Name);
    }
    return permissionSetNames;
  }

  /**
   * Restituisce il contesto dell'utente corrente.
   * Utilizza la property in cache `currentUserContext`.
   *
   * @return `UserContext` dell'utente loggato.
   */
  @AuraEnabled
  public static UserContext getUserContext() {
    return UserUtils.currentUserContext;
  }

  /**
   * Crea un nuovo contesto per l'utente indicato.
   *
   * @param userId Identificativo dell'utente di cui costruire il contesto.
   * @return Istanza appena creata di `UserContext`.
   */
  @AuraEnabled
  public static UserContext getUserContextById(String userId) {
    if (userId == null) {
      throw new AuraHandledException('User ID cannot be null.');
    }
    UserInput input = new UserInput();
    input.userId = userId;
    return new UserContext(input);
  }

  /**
   * Crea un nuovo contesto per l'utente basato sul codice fiscale.
   *
   * @param fiscalCode Codice fiscale dell'utente di cui costruire il contesto.
   * @return Istanza appena creata di `UserContext`.
   */
  @AuraEnabled
  public static UserContext getUserContextByFiscalCode(String fiscalCode) {
    if (fiscalCode == null) {
      throw new AuraHandledException('Fiscal code cannot be null or empty.');
    }

    UserInput input = new UserInput();
    input.fiscalCode = fiscalCode;
    return new UserContext(input);
  }


  /**
   * Crea un nuovo contesto per l'utente basato sul codice utente.
   *
   * @param userCode Codice utente dell'utente di cui costruire il contesto.
   * @return Istanza appena creata di `UserContext`.
   */
  @AuraEnabled
  public static UserContext getUserContextByUserCode(String userCode) {
    if (userCode == null) {
      throw new AuraHandledException('User code cannot be null or empty.');
    }

    UserInput input = new UserInput();
    input.userCode = userCode;
    return new UserContext(input);
  }

  /**
   * Crea un nuovo contesto per l'utente basato sull'input fornito.
   *
   * @param input Oggetto `UserInput` contenente i dati per costruire il contesto.
   * @return Istanza appena creata di `UserContext`.
   */
  @AuraEnabled
  public static UserContext getUserContextByInput(String input) {
    if (input == null) {
      throw new AuraHandledException('User input JSON cannot be null.');
    }

    UserInput userInput = (UserInput) JSON.deserialize(input, UserInput.class);
    System.debug('User Input: ' + userInput);
    return new UserContext(userInput);
  }
}