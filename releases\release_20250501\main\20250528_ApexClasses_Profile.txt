------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
This manual procedure needs to access APEX CLASSES for "Unipol Responsabile User",
"Unipol Standard User" and "System Administrator" profile
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------
1. Login as System Admin to Salesforce
2. Click on the settings icon at the top right and go to the setup item
3. In the quick find bar, find 'Profiles'
For profile 'Unipol Responsabile User'
    1. Click on the 'Unipol Responsabile User' profile
    2. In the 'Find settings' bar, type 'Apex Class Access'
    3. In the 'Apex Class Access' click Edit
    4. Find the Apex Class [See the apex class list above] and move them on Enabled Apex Classes list
    5. Click on 'save'
    6. Repeat step 1-5 with other used profiles (Unipol Standard User and System Admin)
Apex Classes list:
    - IntegrationUtilityAction
    - AnagraficaModificaUpdate
    - FEA_Helper
    - FeaController
    - UserNetworkCallable
    - UsersUcaWrapper
    - UserUtils
    - uniUtils
    - OmnistudioUtils