/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 04-25-2025
 * @last modified by  : <EMAIL>
**/
public without sharing class CaseRulesBeforeInsert {

    public static void logicCaseActivityInit(List<Case> newListCase) {
        if (!Schema.sObjectType.CaseActivityInit__c.isAccessible()) {
            return;
        }

        if (newListCase.isEmpty()) {
            return;
        }

        List<CaseActivityInit__c> caseActivityInits = getCaseActivityInits();
        if (caseActivityInits.isEmpty()) {
            return;
        }

        Boolean isTris = newListCase[0].LeoActivityCode__c == null;
        system.debug('%%%% isTris: ' + isTris);
        Map<String, CaseActivityInit__c> manualCase = new Map<String, CaseActivityInit__c>();
        Map<Integer, CaseActivityInit__c> externalCase = new Map<Integer, CaseActivityInit__c>();

        populateCaseMaps(caseActivityInits, manualCase, externalCase, isTris);

        if (isTris) {
            system.debug('%%%% manualCase: ' + manualCase);
            processManualCases(newListCase, manualCase);
        } else {
            processExternalCases(newListCase, externalCase);
        }
    }

    private static List<CaseActivityInit__c> getCaseActivityInits() {
        return [SELECT IsDeleted, Name, OwnerId,CreatedDate,CreatedById, LastModifiedDate, LastModifiedById,SystemModstamp, LastActivityDate, LeoCode__c, NeedsCloseCallout__c, Source__c, CCEngaged__c, PossibleAssignemnt__c, 
                AssignmentRules__c, LeoHeat__c, LeoPriority__c, ShowOutcome__c, IsPlannable__c, Nature__c, Area__c, ClosedDate__c, Activity__c, Detail__c, 
                AreaOfNeed__c, DueDateDays__c, IsCallBack__c, IsReservedArea__c, RequiredPolicy__c, RequiredIncident__c, GetInTouch__c, ShowCloseManual__c, Show_Button_Certifica_Modifica__c, Show_Button_Download_Doc__c, Show_Button_Missing_Doc__c, Show_Button_Send_SMS__c,
                OverrideAgecy__c FROM CaseActivityInit__c WHERE OverrideAgecy__c = null AND LeoCode__c!= null ];
    }

    private static void populateCaseMaps(List<CaseActivityInit__c> caseActivityInits, Map<String, CaseActivityInit__c> manualCase, Map<Integer, CaseActivityInit__c> externalCase, Boolean isTris) {
        for (CaseActivityInit__c caseActInit : caseActivityInits) {
            if (isTris) {
                System.debug('&&: ' + caseActInit.Area__c + '-' + caseActInit.Activity__c + '-' + caseActInit.Detail__c);
                manualCase.put(caseActInit.Area__c + '-' + caseActInit.Activity__c + '-' + caseActInit.Detail__c, caseActInit);
            } else {
                externalCase.put(Integer.valueOf(caseActInit.LeoCode__c), caseActInit);
            }
        }
    }

    private static void processManualCases(List<Case> newListCase, Map<String, CaseActivityInit__c> manualCase) {
        for (Case c : newListCase) {
            String key = c.Area__c + '-' + c.Activity__c + '-' + c.Detail__c;
            system.debug('%%%% key: ' + key);
            system.debug('%%%% manualCase: ' + manualCase);
            system.debug('%%%% manualCase.containsKey(key): ' + manualCase.containsKey(key));
            system.debug('%%%% manualCase.get(key): ' + manualCase.get(key));
            if (manualCase.containsKey(key)) {
                CaseActivityInit__c matchedRecord = manualCase.get(key);
                populateFieldsIfEmpty(c, matchedRecord);
            }
            c.StartDate__c = System.TODAY();
            String recordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('AttivitaContatto').getRecordTypeId();
            c.RecordTypeId = (c.RecordTypeId == null) ? recordTypeId : c.RecordTypeId;
        }
    }

    private static void processExternalCases(List<Case> newListCase, Map<Integer, CaseActivityInit__c> externalCase) {
        for (Case c : newListCase) {
            if (externalCase.containsKey(Integer.valueOf(c.LeoActivityCode__c))) {
                CaseActivityInit__c matchedRecord = externalCase.get(Integer.valueOf(c.LeoActivityCode__c));
                populateFieldsIfEmpty(c, matchedRecord);
            }
            c.StartDate__c = (c.StartDate__c == null) ? System.today() : c.StartDate__c;
            String recordTypeId = Schema.SObjectType.Case.getRecordTypeInfosByName().get('AttivitaContatto').getRecordTypeId();
            c.RecordTypeId = (c.RecordTypeId == null) ? recordTypeId : c.RecordTypeId;
        }
    }

    public static void populateFieldsIfEmpty(Case c, CaseActivityInit__c matchedRecord) {
        if (fieldMappingCache == null) {
            initializeFieldCache();
        }
        
        DateTime now = DateTime.now();

        for (String sourceField : fieldMappingCache.keySet()) {
            String targetField = fieldMappingCache.get(sourceField);
            
            if (handleSpecialFields(c, matchedRecord, sourceField, targetField, now)) {
                continue;
            }

            Object sourceValue = matchedRecord.get(sourceField);
            Object targetValue = c.get(targetField);
            
            if (sourceValue != null && isFieldEmpty(targetValue)) {
                c.put(targetField, sourceValue);
            }
        }
    }

    private static Map<String, String> fieldMappingCache;
    private static Set<String> excludedFieldsCache;

    private static void initializeFieldCache() {
        fieldMappingCache = new Map<String, String>();
        excludedFieldsCache = new Set<String>(getExcludedFields());
        
        Map<String, String> fullMapping = getFieldMapping();
        Map<String, SObjectField> caseFields = Case.SObjectType.getDescribe().fields.getMap();

        for (String sourceField : fullMapping.keySet()) {
            String targetField = fullMapping.get(sourceField);

            if (caseFields.containsKey(targetField) && 
                !excludedFieldsCache.contains(sourceField) &&
                isFieldUpdateable(caseFields.get(targetField))) {
                fieldMappingCache.put(sourceField, targetField);
            }
        }

        Map<String, SObjectField> sourceFields = CaseActivityInit__c.SObjectType.getDescribe().fields.getMap();
        for (String fieldName : sourceFields.keySet()) {
            if (!fieldMappingCache.containsKey(fieldName) && 
                !excludedFieldsCache.contains(fieldName) &&
                caseFields.containsKey(fieldName) &&
                isFieldUpdateable(caseFields.get(fieldName))) {
                fieldMappingCache.put(fieldName, fieldName);
            }
        }
    }
    
    private static Boolean isFieldUpdateable(SObjectField field) {
        return field.getDescribe().isUpdateable() && field.getDescribe().isCreateable();
    }

    private static Boolean isFieldEmpty(Object fieldValue) {
        return fieldValue == null || 
               (fieldValue instanceof Boolean && !(Boolean) fieldValue) ||
               (fieldValue instanceof String && String.isBlank((String) fieldValue));
    }

    public static Boolean handleSpecialFields(Case c, CaseActivityInit__c matchedRecord, String fieldName, String caseFieldName, DateTime now) {
        SpecialFieldHandler handler = new SpecialFieldHandler(c, matchedRecord, now);
        return handler.processField(fieldName, caseFieldName);
    }

    private class SpecialFieldHandler {
        private Case caseRecord;
        private CaseActivityInit__c matchedRecord;
        private DateTime now;
        
        public SpecialFieldHandler(Case caseRecord, CaseActivityInit__c matchedRecord, DateTime now) {
            this.caseRecord = caseRecord;
            this.matchedRecord = matchedRecord;
            this.now = now;
        }
        
        public Boolean processField(String fieldName, String caseFieldName) {
            if (isDateField(fieldName)) {
                return handleDateFields(fieldName, caseFieldName);
            }
            
            if (isCallbackField(fieldName)) {
                return handleCallbackFields();
            }
            
            return false;
        }
        
        private Boolean isDateField(String fieldName) {
            return fieldName == 'ClosedDate__c' || fieldName == 'DueDateDays__c';
        }
        
        private Boolean isCallbackField(String fieldName) {
            return fieldName == 'IsCallBack__c' || fieldName == 'IsReservedArea__c';
        }
        
        private Boolean handleDateFields(String fieldName, String caseFieldName) {
            Object currentValue = caseRecord.get(caseFieldName);
            if (!shouldUpdateDateField(currentValue)) {
                return true;
            }
            
            Decimal daysToAdd = (Decimal) matchedRecord.get(fieldName);
            if (daysToAdd == null) {
                return true;
            }
            
            if (fieldName == 'ClosedDate__c') {
                setClosedDate(daysToAdd);
            } else if (fieldName == 'DueDateDays__c') {
                setDueDate(daysToAdd);
            }
            
            return true;
        }
        
        private Boolean shouldUpdateDateField(Object currentValue) {
            return currentValue == null || currentValue == 'DefaultValue';
        }
        
        private void setClosedDate(Decimal daysToAdd) {
            caseRecord.put('ClosedDate__c', now.addDays(daysToAdd.intValue()).date());
        }
        
        private void setDueDate(Decimal daysToAdd) {
            caseRecord.put('DueDate__c', now.addDays(daysToAdd.intValue()).date());
        }
        
        private Boolean handleCallbackFields() {
            Boolean isCallBack = getBoolean('IsCallBack__c');
            Boolean isReservedArea = getBoolean('IsReservedArea__c');
            
            if (isCallBack || isReservedArea) {
                caseRecord.put('Requestfromclient__c', true);
            }
            return true;
        }
        
        private Boolean getBoolean(String fieldName) {
            Boolean value = (Boolean) matchedRecord.get(fieldName);
            return Boolean.valueOf(value);
        }
    }

    private static Map<String, String> getFieldMapping() {
        return new Map<String, String>{
            'PossibleAssignemnt__c'             => 'TECH_PossibleAssignemnt__c',
            'RequiredPolicy__c'                 => 'TECH_RequiredPolicy__c',
            'RequiredIncident__c'               => 'TECH_RequiredIncident__c',
            'ShowCloseManual__c'                => 'TECH_ShowCloseManual__c',
            'ShowOutcome__c'                    => 'TECH_ShowOutcome__c',
            'IsPlannable__c'                    => 'TECH_IsPlannable__c',
            'LeoHeat__c'                        => 'TECH_LeoHeat__c',
            'LeoPriority__c'                    => 'TECH_LeoPriority__c',
            'NeedsCloseCallout__c'              => 'TECH_NeedsCloseCallout__c',
            'IsCallBack__c'                     => 'Requestfromclient__c',
            'IsReservedArea__c'                 => 'Requestfromclient__c',
            'AssignmentRules__c'                => 'TECH_AssignmentRules__c',
            'DueDateDays__c'                    => 'DueDate__c',
            'GetInTouch__c'                     => 'TECH_getInTouch__c',
            'Show_Button_Missing_Doc__c'        => 'TECH_ShowButtonMissingDocument__c',
            'Show_Button_Certifica_Modifica__c' => 'TECH_ShowButtonCertificaModifica__c',
            'Show_Button_Download_Doc__c'       => 'TECH_ShowButtonDownloadDoc__c',
            'Show_Button_Send_SMS__c'           => 'TECH_ShowButtonSendSMS__c'
        };
    }

    private static List<String> getExcludedFields() {
        return new List<String>{
            'CreatedDate', 'CreatedById', 'LastModifiedDate', 'LastModifiedById',
            'SystemModstamp', 'LastActivityDate', 'OwnerId', 'IsDeleted', 'Name',
            'Id'
        };
    }

    public static void setCasesPriority(List<Case> newListCase) {
        if (newListCase == null || newListCase.isEmpty()) {
            return;
        }

        PriorityConfiguration config = loadPriorityConfiguration();
        
        if (config.isEmpty()) {
            return;
        }
        
        Date today = Date.today();
        for (Case c : newListCase) {
            Decimal priority = calculatePriority(c, config, today);
            assignPriority(c, priority);
        }
    }

    private static PriorityConfiguration loadPriorityConfiguration() {
        PriorityConfiguration config = new PriorityConfiguration();
        
        List<CaseActivityInit__c> configurations = [SELECT Type__c, WeightAttribute__c, WeightValue__c, Area__c, Activity__c, LeoHeat__c, LeoPriority__c
            FROM CaseActivityInit__c WHERE Type__c IN ('Weight', 'Score')];
        
        for (CaseActivityInit__c conf : configurations) {
            if (conf.Type__c == 'Weight') {
                config.weightMap.put(conf.WeightAttribute__c, conf.WeightValue__c);
            } else if (conf.Type__c == 'Score') {
                if (!String.isEmpty(conf.Area__c) && !String.isEmpty(conf.Activity__c)) {
                    config.activityScoreMap.put(conf.Area__c + '-' + conf.Activity__c, conf.WeightValue__c);
                } else if (conf.LeoHeat__c != null && conf.LeoPriority__c != null) {
                    config.heatScoreMap.put(conf.LeoHeat__c + '-' + conf.LeoPriority__c, conf.WeightValue__c);
                }
            }
        }
        return config;
    }

    private static Decimal calculatePriority(Case c, PriorityConfiguration config, Date today) {
        Decimal weightActivity = config.weightMap.get('ATTIVITA') ?? 0;
        Decimal weightHeat = config.weightMap.get('CALORE-FLAG') ?? 0;
        Decimal weightDueDate = config.weightMap.get('SCADENZA') ?? 0;

        Decimal scoreActivity = config.activityScoreMap.get(c.Area__c + '-' + c.Activity__c) ?? 0;
        Decimal scoreHeat = config.heatScoreMap.get(c.TECH_LeoHeat__c + '-' + c.TECH_LeoPriority__c) ?? 0;
        Decimal scoreDueDate = calculateDueDateScore(c.DueDate__c, today);
        
        return (weightActivity * scoreActivity) + (weightHeat * scoreHeat) + (weightDueDate * scoreDueDate);
    }

    private static Decimal calculateDueDateScore(Date dueDate, Date today) {
        if (dueDate == null) {
            return 0;
        }
        
        Integer daysFromToday = today.daysBetween(dueDate);
        if (daysFromToday <= 3) {
            return 1;
        } else if (daysFromToday <= 7) {
            return 0.66;
        } else if (daysFromToday <= 14) {
            return 0.33;
        } else {
            return 0;
        }
    }

    private static void assignPriority(Case c, Decimal priorityValue) {
        String newPriority;
        
        if (priorityValue >= 0.7) {
            newPriority = 'High';
        } else if (priorityValue >= 0.4) {
            newPriority = 'Medium';
        } else {
            newPriority = 'Low';
        }

        if (c.Priority != newPriority) {
            c.Priority = newPriority;
        }
    }
    
    private class PriorityConfiguration {
        public Map<String, Decimal> weightMap = new Map<String, Decimal>();
        public Map<String, Decimal> activityScoreMap = new Map<String, Decimal>();
        public Map<String, Decimal> heatScoreMap = new Map<String, Decimal>();
        
        public Boolean isEmpty() {
            return weightMap.isEmpty() && activityScoreMap.isEmpty() && heatScoreMap.isEmpty();
        }
    }

    public static void populateAgencyOnAutomaticCase(List<Case> newListCase) {
        if (newListCase == null || newListCase.isEmpty()) {
            return;
        }

        ExternalIdCollectionResult collectionResult = collectExternalIdsAndGroupCases(newListCase);
        
        if (collectionResult.allExternalIds.isEmpty()) {
            return;
        }

        Map<String, Account> accountsByExternalId = fetchAccountsByExternalIds(collectionResult.allExternalIds);

        assignAccountsAndAgenciesToCases(collectionResult, accountsByExternalId);
    }

    private static ExternalIdCollectionResult collectExternalIdsAndGroupCases(List<Case> newListCase) {
        ExternalIdCollectionResult result = new ExternalIdCollectionResult();
        for (Case c : newListCase) {
            processAgencyExternalId(c, result);
            processCfPivaExternalId(c, result);
        }
        return result;
    }

    private static void processAgencyExternalId(Case c, ExternalIdCollectionResult result) {
        if (!String.isBlank(c.DrAgenziaFiglia__c)) {
            String agencyExternalId = 'AGE_' + c.DrAgenziaFiglia__c;
            result.allExternalIds.add(agencyExternalId);
            addCaseToGroup(result.agencyToCases, agencyExternalId, c);
        }
    }

    private static void processCfPivaExternalId(Case c, ExternalIdCollectionResult result) {
        if (!String.isBlank(c.DrCfPivaCliente__c)) {
            result.allExternalIds.add(c.DrCfPivaCliente__c);
            addCaseToGroup(result.cfPivaToCases, c.DrCfPivaCliente__c, c);
        }
    }

    private static void addCaseToGroup(Map<String, List<Case>> groupMap, String key, Case c) {
        if (!groupMap.containsKey(key)) {
            groupMap.put(key, new List<Case>());
        }
        groupMap.get(key).add(c);
    }

    private static Map<String, Account> fetchAccountsByExternalIds(Set<String> allExternalIds) {
        if (!Schema.sObjectType.Account.isAccessible()) {
            return new Map<String, Account>();
        }
        
        Map<String, Account> accountsByExternalId = new Map<String, Account>();
        List<Account> accounts = [SELECT Id, ExternalId__c, RecordType.DeveloperName FROM Account WHERE ExternalId__c IN :allExternalIds];
        
        for (Account acc : accounts) {
            if (isValidRecordType(acc.RecordType.DeveloperName)) {
                accountsByExternalId.put(acc.ExternalId__c, acc);
            }
        }
        return accountsByExternalId;
    }

    private static void assignAccountsAndAgenciesToCases(ExternalIdCollectionResult collectionResult, Map<String, Account> accountsByExternalId) {
        assignAgencyToRelatedCases(collectionResult.agencyToCases, accountsByExternalId);
        assignAccountToRelatedCases(collectionResult.cfPivaToCases, accountsByExternalId);
    }

    private static Boolean isValidRecordType(String recordTypeName) {
        return recordTypeName == 'PersonAccount' || 
               recordTypeName == 'IndustriesBusiness' || 
               recordTypeName == 'Agency';
    }

    private static void assignAgencyToRelatedCases(Map<String, List<Case>> agencyToCases, Map<String, Account> accountsByExternalId) {
        for (String agencyExternalId : agencyToCases.keySet()) {
            if (accountsByExternalId.containsKey(agencyExternalId)) {
                Id agencyId = accountsByExternalId.get(agencyExternalId).Id;
                for (Case c : agencyToCases.get(agencyExternalId)) {
                    c.Agency__c = agencyId;
                }
            }
        }
    }

    private static void assignAccountToRelatedCases(Map<String, List<Case>> cfPivaToCases, Map<String, Account> accountsByExternalId) {
        for (String cfPiva : cfPivaToCases.keySet()) {
            if (accountsByExternalId.containsKey(cfPiva)) {
                Id accountId = accountsByExternalId.get(cfPiva).Id;
                for (Case c : cfPivaToCases.get(cfPiva)) {
                    c.AccountId = accountId;
                }
            }
        }
    }

    public static void updateStatusToNew(List<Case> cases){
        for(Case c : cases){
            if(c.dueDate__c >= System.today() && c.Status == 'Expired'){
                c.Status = 'New';
            }
        }
    }

    public static void updateStatusToClosed(List<Case> cases){
        for(Case c : cases){
            if(c.ClosedDate__c == System.today()) {
                c.Status = 'Closed';
            }
        }
    }

    public static void updateStatusToExpiredDueDate(List<Case> cases){
        for(Case c : cases){
            if(c.DueDate__c != null && c.DueDate__c <= System.today().addDays(-1)) {
                c.Status = 'Expired';
            }
        }
    }
    private class ExternalIdCollectionResult {
        public Set<String> allExternalIds = new Set<String>();
        public Map<String, List<Case>> agencyToCases = new Map<String, List<Case>>();
        public Map<String, List<Case>> cfPivaToCases = new Map<String, List<Case>>();
    }

    public static void assignDefaultOwner(List<Case> cases) {
        if (cases == null || cases.isEmpty()) {
            return;
        }
        if (Schema.sObjectType.User.isAccessible()) {
            User u = [SELECT Id, Name FROM User WHERE (Name = :System.Label.OwnerId OR Name = :System.Label.OwnerId2) LIMIT 1];
            for (Case obj : cases) {
                obj.OwnerId = u.Id;
            }
        }
    }
    
    public static List<Case> filterCasesByLeoActivityCode(List<Case> cases) {
        Set<String> leoCodes = new Set<String>();
        for (Case c : cases) {
            if (!String.isBlank(c.LeoActivityCode__c)) {
                leoCodes.add(c.LeoActivityCode__c);
            }
        }

        Set<String> validCodes = new Set<String>();
        for (CaseActivityInit__c cai : [SELECT LeoCode__c FROM CaseActivityInit__c WHERE Type__c = 'Contact' AND LeoCode__c != null AND LeoCode__c IN :leoCodes]) {
            validCodes.add(cai.LeoCode__c);
        }

        List<Case> filteredCases = new List<Case>();
        for (Case c : cases) {
            if (!String.isBlank(c.LeoActivityCode__c) && validCodes.contains(c.LeoActivityCode__c)) {
                filteredCases.add(c);
            }
        }
        return filteredCases;
    }

    public static void checkLookupAccountField(List<Case> cases){
        if (!Schema.sObjectType.Case.isDeletable()) {
            return;
        }
        Set<Id> caseIds = new Set<Id>();
        for (Case c : cases) {
            caseIds.add(c.Id);
        }
        List<Case> casesToDelete = [SELECT Id FROM Case WHERE Id IN :caseIds AND AccountId = null];
        if (!casesToDelete.isEmpty()) {
            delete casesToDelete;
        }
    }
}