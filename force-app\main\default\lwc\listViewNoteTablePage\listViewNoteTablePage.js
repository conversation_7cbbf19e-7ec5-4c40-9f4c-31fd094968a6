import { LightningElement, api, track, wire } from 'lwc';
import getNotesJson from '@salesforce/apex/NoteController.getNotesJson';
import deleteNoteCase from '@salesforce/apex/NoteController.deleteNoteCase';
import updateNoteCase from '@salesforce/apex/NoteController.updateCaseNote';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { CurrentPageReference } from 'lightning/navigation';

export default class ListViewNoteTablePage extends LightningElement {
    @api recordId;
    @track noteData = [];
    @track showEditModal = false;
    @track showDeleteModal = false;
    @track showViewModal = false;
    @track editedNote = { Title: '', Body: '', recordId: null };
    @track noteToDelete = null;
    //@wire (CurrentPageReference) currentPageReference;
    @track idForNotes = null;
    @track isCase = false;
    @wire(CurrentPageReference)
    getIdForNotes(currentPageReference) {
        this.isCase = currentPageReference?.state?.ws?.includes('Case');
        if (currentPageReference?.attributes?.recordId) {
            this.idForNotes = currentPageReference.attributes.recordId;
        } else {
            const ws = currentPageReference?.state?.ws;
            if (this.isCase) {
                this.idForNotes = ws?.split('Case/')[1]?.split('/view')[0];
            }
        }
    }

    columns = [
        {
            label: 'Nome',
            fieldName: 'Title',
            type: 'button',
            typeAttributes: {
                label: { fieldName: 'Title' },
                name: 'view',
                variant: 'base'
            }
        },
        { label: 'Contenuto', fieldName: 'Body' },
        {
            label: 'Creato da',
            fieldName: 'createLink',
            type: 'url',
            typeAttributes: { label: { fieldName: 'CreatedBy' }, target: '_blank' }
        },
        { label: 'Data Creazione', fieldName: 'CreatedDateFormatted' },
        { label: 'Data Ultima modifica', fieldName: 'LastModifiedDateFormatted' },
        {
            label: 'Ultima modifica da',
            fieldName: 'lastEditLink',
            type: 'url',
            typeAttributes: { label: { fieldName: 'LastModifiedBy' }, target: '_blank' }
        },
        {
            type: 'action',
            typeAttributes: {
                rowActions: [
                    { label: 'Modifica', name: 'edit' },
                    { label: 'Elimina', name: 'delete' }
                ]
            }
        }
    ];

    connectedCallback() {
        this.loadNotes();
    }
    
    /*get isCase() {
        return this.currentPageReference?.state?.ws?.includes('Case');
    }
    
    get testId() {
        const ws = this.currentPageReference?.state?.ws;
        if (this.isCase) {
            return ws?.split('Case/')[1]?.split('/view')[0];
        }     
    }*/
    
    async loadNotes() {
        try {
            const testId = this.idForNotes;
            const data = await getNotesJson({ recordId: testId });
            const parsed = JSON.parse(data);
            const noteRaw = parsed?.[0]?.Note || parsed;
            const notes = Array.isArray(noteRaw) ? noteRaw : [noteRaw];
            this.noteData = notes.map((note, index) => ({
                id: index,
                Title: note.Title,
                Body: note.Body,
                CreatedBy: note.CreatedBy,
                LastModifiedBy: note.LastModifiedBy,
                LastModifiedDate: note.LastModifiedDate,
                CreatedDateFormatted: this.formatDate(note.CreatedDate),
                LastModifiedDateFormatted: this.formatDate(note.LastModifiedDate),
                createLink: '/' + note.IdCreated,
                lastEditLink: '/' + note.IdModified,
                recordId: note.Id
            }));
        } catch (error) {
             console.error('Errore nel caricamento note:', error);
            const errorMessage = error?.body?.message || error?.message || 'Errore generico nel caricamento note.';
            this.showToast('Errore', errorMessage, 'error');
            //this.showToast('Errore', 'Errore nel caricamento delle note', 'error');
        }
    }

    formatDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('it-IT') + ', ' + date.toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' });
    }

    handleRowAction(event) {
        const action = event.detail.action.name;
        const row = event.detail.row;

        if (action === 'edit') {
            this.editedNote = { Title: row.Title, Body: row.Body, recordId: row.recordId };
            this.showEditModal = true;
        } else if (action === 'delete') {
            this.noteToDelete = row;
            this.showDeleteModal = true;
        }else if (action === 'view') {
            this.selectedNote = { Title: row.Title, Body: row.Body, recordId: row.recordId };
            this.showViewModal = true;
        }
        console.log(this.showViewModal);
        console.log(this.showEditModal);
        console.log(this.showDeleteModal);
    }

    handleTitleChange(event) {
        this.editedNote.Title = event.detail.value;
    }

    handleBodyChange(event) {
        this.editedNote.Body = event.detail.value;
    }

    closeEditModal() {
        this.editedNote = { Title: '', Body: '', recordId: null };
        this.showEditModal = false;
    }

    async saveEditedNote() {
            const { recordId, Title, Body } = this.editedNote;
            try {
                if (this.isCase) {
                    await updateNoteCase({ noteId: recordId, title: Title, body: Body });
                }
                this.showToast('Nota aggiornata', 'La nota è stata modificata con successo.', 'success');
                this.showEditModal = false;
                this.loadNotes();
            } catch (error) {
                console.error('Errore modifica:', error);
                this.showToast('Errore', 'Errore durante la modifica della nota.', 'error');
            }
        }
    
        cancelDelete() {
            this.noteToDelete = null;
            this.showDeleteModal = false;
        }
    
        async confirmDelete() {
            try {
                if (!this.noteToDelete) {
                    this.showToast('Errore', 'Nessuna nota selezionata per la cancellazione', 'error');
                    return;
                }
                const payload = { Id: this.noteToDelete.recordId };
                if (this.isCase) {
                    await deleteNoteCase({ recordId: payload.Id });
                }
                this.showToast('Nota eliminata', `${this.noteToDelete.Title} è stata eliminata.`, 'success');
                this.noteToDelete = null;
                this.showDeleteModal = false;
                this.loadNotes();
            } catch (error) {
                console.error('Errore eliminazione:', error);
                const errorMessage = error.body ? error.body.message : 'Errore durante l\'eliminazione';
                this.showToast('Errore', errorMessage, 'error');
            }
        } 
    
    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({ title, message, variant }));
    }

    openEditFromView() {
        this.editedNote = { ...this.selectedNote };
        this.showEditModal = true;
        this.showViewModal = false;
    }

    closeViewModal() {
        this.showViewModal = false;
        this.selectedNote = { Title: '', Body: '', recordId: null };
    }
}