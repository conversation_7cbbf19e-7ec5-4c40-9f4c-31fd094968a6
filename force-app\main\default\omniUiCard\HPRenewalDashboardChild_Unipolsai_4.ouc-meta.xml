<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;[\n  {\n    \&quot;emptyIncassati\&quot;: false,\n    \&quot;label\&quot;: \&quot;Incassati\&quot;,\n    \&quot;numero\&quot;: 800,\n    \&quot;premio\&quot;: \&quot;€1.200.000,00\&quot;\n  },\n  {\n    \&quot;emptyIncassati\&quot;: false,\n    \&quot;label\&quot;: \&quot;Da incassare\&quot;,\n    \&quot;numero\&quot;: 250,\n    \&quot;premio\&quot;: \&quot;€342.300,50\&quot;\n  }\n]\n&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Parent.period&quot;,&quot;val&quot;:&quot;fromTo&quot;,&quot;id&quot;:21},{&quot;name&quot;:&quot;Parent.fromDate&quot;,&quot;val&quot;:&quot;2025-01-01&quot;,&quot;id&quot;:22},{&quot;name&quot;:&quot;Parent.toDate&quot;,&quot;val&quot;:&quot;2025-02-01&quot;,&quot;id&quot;:23},{&quot;name&quot;:&quot;Parent.section&quot;,&quot;val&quot;:&quot;RCA;VIT&quot;,&quot;id&quot;:24},{&quot;name&quot;:&quot;Parent.rate&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:25},{&quot;name&quot;:&quot;Parent.referent&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:7},{&quot;name&quot;:&quot;Parent.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:27}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>HPRenewalDashboardChild</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cspan%20style=%22font-size:%2012pt;%22%3ENessun%20risultato%20trovato%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-192&quot;,&quot;field&quot;:&quot;emptyIncassati&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-199&quot;,&quot;field&quot;:&quot;emptyNonIncassati&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Text-0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[],&quot;elementLabel&quot;:&quot;Block-1&quot;},{&quot;name&quot;:&quot;Chart&quot;,&quot;element&quot;:&quot;flexChart&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;type&quot;:&quot;doughnut&quot;,&quot;hideHeader&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;records&quot;:&quot;{records}&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;title&quot;:&quot;Chart&quot;,&quot;channel&quot;:&quot;flexcarddesigner:chart&quot;,&quot;cutoutPercentage&quot;:&quot;0&quot;,&quot;colorPalette&quot;:[&quot;#52B7D8&quot;,&quot;#E16032&quot;,&quot;#FFB03B&quot;,&quot;#54A77B&quot;,&quot;#4FD2D2&quot;,&quot;#E287B2&quot;],&quot;labelNode&quot;:&quot;label&quot;,&quot;valueNode&quot;:&quot;numero&quot;,&quot;maintainAspectRatio&quot;:false,&quot;aspectRatio&quot;:&quot;1&quot;,&quot;chartHeight&quot;:&quot;&quot;,&quot;hasAccessibilitySupport&quot;:false,&quot;displayLegend&quot;:&quot;true&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-62&quot;,&quot;field&quot;:&quot;emptyIncassati&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-69&quot;,&quot;field&quot;:&quot;emptyNonIncassati&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_10-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;xx-large&quot;,&quot;label&quot;:&quot;right:xx-large&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;xx-large&quot;,&quot;label&quot;:&quot;left:xx-large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-p-right_xx-large slds-m-left_xx-large &quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;height&quot;:&quot;&quot;,&quot;minHeight&quot;:&quot;&quot;,&quot;maxHeight&quot;:&quot;&quot;},&quot;elementLabel&quot;:&quot;Chart-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_10-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;xx-large&quot;,&quot;label&quot;:&quot;right:xx-large&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;xx-large&quot;,&quot;label&quot;:&quot;left:xx-large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;10&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-p-right_xx-large slds-m-left_xx-large &quot;,&quot;style&quot;:&quot;      \n         &quot;,&quot;height&quot;:&quot;&quot;,&quot;minHeight&quot;:&quot;&quot;,&quot;maxHeight&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[],&quot;blankCardState&quot;:false}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;[\n  {\n    \&quot;emptyIncassati\&quot;: false,\n    \&quot;label\&quot;: \&quot;Incassati\&quot;,\n    \&quot;numero\&quot;: 800,\n    \&quot;premio\&quot;: \&quot;€1.200.000,00\&quot;\n  },\n  {\n    \&quot;emptyIncassati\&quot;: false,\n    \&quot;label\&quot;: \&quot;Da incassare\&quot;,\n    \&quot;numero\&quot;: 250,\n    \&quot;premio\&quot;: \&quot;€342.300,50\&quot;\n  }\n]\n&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Parent.period&quot;,&quot;val&quot;:&quot;fromTo&quot;,&quot;id&quot;:21},{&quot;name&quot;:&quot;Parent.fromDate&quot;,&quot;val&quot;:&quot;2025-01-01&quot;,&quot;id&quot;:22},{&quot;name&quot;:&quot;Parent.toDate&quot;,&quot;val&quot;:&quot;2025-02-01&quot;,&quot;id&quot;:23},{&quot;name&quot;:&quot;Parent.section&quot;,&quot;val&quot;:&quot;RCA;VIT&quot;,&quot;id&quot;:24},{&quot;name&quot;:&quot;Parent.rate&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:25},{&quot;name&quot;:&quot;Parent.referent&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:7},{&quot;name&quot;:&quot;Parent.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:27}]},&quot;title&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;isRepeatable&quot;:false,&quot;xmlObject&quot;:{&quot;isExplicitImport&quot;:false,&quot;targetConfigs&quot;:&quot;&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;]}},&quot;sessionVars&quot;:[],&quot;xmlJson&quot;:[],&quot;events&quot;:[{&quot;eventname&quot;:&quot;reload&quot;,&quot;channelname&quot;:&quot;HPRenewalDashboardChild&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1753319037247-apy813qap&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753319037271&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;HPRenewalDashboardChild:reload&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}],&quot;hideChildCardPreview&quot;:false,&quot;osSupport&quot;:true}</propertySetConfig>
    <sampleDataSourceResponse>[{&quot;emptyIncassati&quot;:false,&quot;label&quot;:&quot;Incassati&quot;,&quot;numero&quot;:800,&quot;premio&quot;:&quot;€1.200.000,00&quot;},{&quot;emptyIncassati&quot;:false,&quot;label&quot;:&quot;Da incassare&quot;,&quot;numero&quot;:250,&quot;premio&quot;:&quot;€342.300,50&quot;}]</sampleDataSourceResponse>
    <versionNumber>4</versionNumber>
</OmniUiCard>
