import { LightningElement, api } from 'lwc';

export default class MenuStrumentiFei extends LightningElement {

    @api recordId;
    @api FEIID;
    @api FiscalCode;
    @api feiRequestPayload;
    @api permission;
    @api permissionSetName;
    @api feiAddressEndpointPayload;
    @api cfcontid;
    @api UserId;
    @api society;
    @api feilink;

    async connectedCallback(){

        await new Promise(resolve =>
            setTimeout(resolve, 500)
        );

        console.log('FEIID --> ',this.FEIID);
        console.log('FiscalCode --> ',this.FiscalCode);
        console.log('recordId --> ',this.recordId);
        console.log('feiRequestPayload --> ',this.feiRequestPayload);
        console.log('permissionSetName --> ',this.permissionSetName);
        console.log('society --> ',this.society);
        console.log('cfcontid --> ',this.cfcontid);

        this.feiRequestPayload = this.feiRequestPayload ? JSON.stringify(this.feiRequestPayload) : null;
    }
}