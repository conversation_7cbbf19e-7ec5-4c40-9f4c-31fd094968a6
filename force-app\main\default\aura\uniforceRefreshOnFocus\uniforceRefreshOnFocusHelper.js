({
    executeLogic: function(component) {
        console.log('EXECUTE LOGIC uniforceRefreshOnFocusHelper.js');
        var self = this;
        document.addEventListener("visibilitychange", () => {
            self.refreshLogic(component);
        });        
    },
    executeLogicForInternalTab: function(component) {
        console.log('EXECUTE LOGIC  ForInternalTab');
        // Richiama la logica di refresh
        this.refreshLogic(component);
    },
    
    // Metodo privato per la logica di refresh
    refreshLogic: function(component) {
        // Controlla se la pagina è visibile
        if (document.visibilityState === "visible") {
            let pathUrl = window.location.pathname;
            if (pathUrl 
              && (pathUrl.includes('/Opportunity/') || pathUrl.includes('/Account/') || pathUrl.includes('/Case/') || pathUrl.includes('/Menu_Strumenti')) 
              && !pathUrl.includes('/new') // <--NEW CHECK 
            ) {
                    console.log('Executing refresh logic...');
                    var workspaceAPI = component.find("workspace");
                    workspaceAPI.getFocusedTabInfo().then(function(response) {
                        var focusedTabId = response.tabId;
                        workspaceAPI.refreshTab({
                            tabId: focusedTabId,
                            includeAllSubtabs: true
                        });
                        // Evento di Refresh per Lightning Component
                        $A.get('e.force:refreshView').fire();
                        console.log('Refresh Done');
                    }).catch(function(error) {
                        console.log(error);
                    });
                }
        }
    },

closeModalFEI: function(component) {
    console.log('closeModalFEI called');
    let attempts = 0;
    let maxAttempts = 100;
    let interval = setInterval(function() {
        let found = false;

        // Recursive function to search and click the close button in all shadowRoots
        function findAndClickCloseButtonInShadowRoots(root) {
            if (!root) return false;
            let btn = root.querySelector && root.querySelector('button[title="Chiudi anteprima"]');
            if (btn) {
                btn.click();
                console.log('Close button clicked in shadowRoot or main document');
                return true;
            }
            let children = root.children || [];
            for (let i = 0; i < children.length; i++) {
                let child = children[i];
                if (child.shadowRoot) {
                    if (findAndClickCloseButtonInShadowRoots(child.shadowRoot)) {
                        return true;
                    }
                }
                if (findAndClickCloseButtonInShadowRoots(child)) {
                    return true;
                }
            }
            return false;
        }

        // Search in the main document and its shadowRoots
        if (findAndClickCloseButtonInShadowRoots(document)) {
            found = true;
            console.log('Modal closed via button in main document or shadowRoot');
        }

        // Search in all accessible iframes and their shadowRoots
        Array.from(document.querySelectorAll('iframe')).forEach(function(iframe) {
            try {
                var doc = iframe.contentDocument || iframe.contentWindow.document;
                if (findAndClickCloseButtonInShadowRoots(doc)) {
                    found = true;
                    console.log('Modal closed via button in iframe or its shadowRoot');
                }
            } catch (e) {
                // Ignore cross-origin iframes
            }
        });

        if (found) {
            clearInterval(interval);
            console.log('EXECUTE LOGIC FOR CLOSING MODAL - DONE');
        } else if (++attempts >= maxAttempts) {
            clearInterval(interval);
            console.log('Modal not found after polling');
        }
    }, 200);
}


})