<template>
    <ul class="root">
        <template for:each={itemsProcessed} for:item="itm">
            <li key={itm.developerName} class="item">
                <template lwc:if={itm.isLeaf}>
                    <div class="item-leaf">
                        <template lwc:if={itm.disabled}>
                            <span class="disabled">{itm.label} 🚫</span>
                        </template>
                        <template lwc:else>
                            <a href="#" data-id={itm.developerName} onclick={handleLeafClick}>
                                <span>{itm.label}</span>
                                <lightning-icon icon-name="utility:new_window" size="x-small" class="chevron padding-left_5"></lightning-icon>
                            </a>
                            <template lwc:if={itm.isFavorite}>
                                <lightning-icon icon-name="utility:favorite" size="x-small" class="chevron favorite-colored padding-left_5" data-id={itm.developerName} onclick={handleUnfavoriteClick}></lightning-icon>
                            </template>
                            <template lwc:else>
                                <lightning-icon icon-name="utility:favorite_alt" size="x-small" class="chevron favorite padding-left_5" data-id={itm.developerName} onclick={handleFavoriteClick}></lightning-icon>
                            </template>
                        </template>
                    </div>
                </template>
                <template lwc:else>
                    <div class="parent" onclick={toggleBranch} data-id={itm.developerName}>
                        <span>{itm.label}</span>
                        <lightning-icon icon-name="utility:chevronright" size="x-small" class="chevron">
                        </lightning-icon>
                    </div>

                    <template lwc:if={itm.expanded}>
                        <ul class="popup">
                            <c-menu-strumenti-branch items={itm.children} user-network={userNetwork}></c-menu-strumenti-branch>
                        </ul>
                    </template>
                </template>
            </li>
        </template>
    </ul>
</template>