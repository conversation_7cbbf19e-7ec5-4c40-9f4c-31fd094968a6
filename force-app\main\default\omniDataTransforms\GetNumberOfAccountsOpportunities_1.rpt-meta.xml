<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>GetNumberOfAccountsOpportunities</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| &quot;SELECT/\/\/COUNT()/\/\/FROM/\/\/Opportunity/\/\/WHERE/\/\/AccountId/\/\/=/\/\/&apos;{0}&apos;/\/\/AND/\/\/Id/\/\/!=/\/\/&apos;{1}&apos;/\/\/AND/\/\/StageName/\/\/!=/\/\/&apos;Closed&apos;/\/\/AND/\/\/StageName/\/\/!=/\/\/&apos;Chiuso&apos;/\/\/AND/\/\/RecordType.DeveloperName/\/\/IN(&apos;Omnicanale&apos;,&apos;Agenziale&apos;)&quot; var:Opportunity:AccountId var:recordId COUNTQUERY</formulaConverted>
        <formulaExpression>COUNTQUERY(&quot;SELECT COUNT() FROM Opportunity WHERE AccountId = &apos;{0}&apos; AND Id != &apos;{1}&apos; AND StageName != &apos;Closed&apos; AND StageName != &apos;Chiuso&apos; AND RecordType.DeveloperName IN(&apos;Omnicanale&apos;,&apos;Agenziale&apos;)&quot;, Opportunity:AccountId, recordId)</formulaExpression>
        <formulaResultPath>OpportunityCount</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>GetNumberOfAccountsOpportunitiesCustom0jI9O000000v8vJUAQItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetNumberOfAccountsOpportunities</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>recordId</filterValue>
        <globalKey>GetNumberOfAccountsOpportunitiesCustom0jI9O000000v8vJUAQItem1</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Opportunity</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetNumberOfAccountsOpportunities</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Opportunity</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetNumberOfAccountsOpportunitiesCustom0jI9O000000v8vJUAQItem2</globalKey>
        <inputFieldName>Opportunity:AccountId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetNumberOfAccountsOpportunities</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>AccountId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetNumberOfAccountsOpportunitiesCustom0jI9O000000v8vJUAQItem3</globalKey>
        <inputFieldName>OpportunityCount</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetNumberOfAccountsOpportunities</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OpportunityCount</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>GetNumberOfAccountsOpportunitiesCustom0jI9O000000v8vJUAQItem4</globalKey>
        <inputFieldName>Opportunity:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>GetNumberOfAccountsOpportunities</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Opportunity:Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;recordId&quot; : &quot;0069X00000Kdpn8QAB&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>GetNumberOfAccountsOpportunities_3</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
