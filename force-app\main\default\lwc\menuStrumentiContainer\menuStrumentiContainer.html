<template>

    <div lwc:if={isLoading} class="slds-spinner_container" style="position: fixed !important;z-index:999999;">
        <div class="slds-spinner--brand  slds-spinner slds-spinner--large" role="alert">
            <span class="slds-assistive-text">Loading</span>
            <div class="slds-spinner__dot-a"></div>
            <div class="slds-spinner__dot-b"></div>
        </div>
    </div>
    
    <article class="slds-card" style="height: max-content;" onselectnode={handleSelectNode}>
        <div class="slds-tabs_default" style="padding: 20px;">
            <template lwc:if={notEnabled}>
                <span>Non sei abilitato ad alcun tab del Menu Strumenti</span>
            </template>
            <template lwc:else>
                <ul class="slds-tabs_default__nav" role="tablist">
                    <template for:each={_tabs} for:item="tab">
                        <li key={tab.name} class={tab.tabClass} title="Unipol" role="presentation">
                            <a class="slds-tabs_default__link" href="#" role="tab" tabindex="0" aria-selected={tab.show} data-id={tab.name} aria-controls={tab.ariaControls} id={tab.id} onclick={handleTabClick}>
                                <template lwc:if={tab.isUnipol}>
                                    <img src={unipolLogo} style="max-width: 70px;"/>
                                </template>
                                <template lwc:if={tab.isUnisalute}>
                                    <img src={unisaluteLogo} style="max-width: 70px;"/>
                                </template>
                            </a>
                        </li>
                    </template>
                </ul>
                <template for:each={_tabs} for:item="tab">
                    <div key={tab.name} id={tab.ariaControls} class={tab.tabSection} role="tabpanel" aria-labelledby={tab.id}>
                        <c-page-header-cmp
                            icon="custom:custom19"
                            title="Strumenti"    
                        ></c-page-header-cmp>
                        <div class="slds-grid slds-gutters slds-wrap">
                            <template for:each={tab.sections} for:item="section">
                                <template lwc:if={section.sectionShow}>
                                    <div key={section.sectionName} class="slds-col slds-size_1-of-1 slds-medium-size_3-of-12 slds-large-size_3-of-12" data-id={section.sectionName} style="margin-top: 1.5rem;">
                                        <article class="slds-card slds-card_boundary" style="height: fit-content; padding: 15px;">
                                            <span class="section-title" style="padding: 0.2rem;">{section.sectionLabel}</span>
                                            <c-menu-strumenti-branch items={section.sectionMenus}
                                                                        tab-name={tab.name}
                                                                        user-network={userNetwork}
                                                                        section-name={section.sectionName}
                                                                        onbranchtoggle={handleBranchToggle}
                                                                        onfavoriteselected={handleFavoriteSelected}
                                                                        onunfavoriteselected={handleUnfavoriteSelected}
                                                                        onnavigatefei={handleNavigateFei}></c-menu-strumenti-branch>
                                        </article>
                                    </div>
                                </template>
                            </template>
                        </div>
                    </div>
                </template>
            </template>
        </div>
    </article>

    <template lwc:if={isLWCModalOpened}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">FEI</h2>
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Chiudi anteprima" onclick={toggleLWCModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                    </button>
                </header>        
                <div class="slds-modal__content slds-p-around_medium">
                    <p>FEI Container attivo per: {clickedAction}</p>
                    <c-fei-container
                        feiid={paramsFEI.feiId}
                        fiscalcode={paramsFEI.fiscalCode}
                        fei-request-payload={paramsFEI.feiRequestPayload}
                        permission-set-name={paramsFEI.permissionSetName}
                        society={userNetwork.societyCode}
                        feilink={paramsFEI.feiLink}
                        >
                    </c-fei-container>
                </div>
            </div>
        </section>
            
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>